import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Skeleton } from "@/components/ui/skeleton"

export default function ContentLoading() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        {/* Header skeleton */}
        <div className="flex flex-col gap-2">
          <Skeleton className="h-10 w-[250px]" />
          <div className="flex flex-wrap items-center gap-4">
            <Skeleton className="h-9 w-[180px]" />
            <Skeleton className="h-9 w-[180px]" />
            <div className="ml-auto">
              <Skeleton className="h-9 w-[120px]" />
            </div>
          </div>
        </div>

        {/* Analytics cards skeleton */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {Array(4)
            .fill(null)
            .map((_, i) => (
              <Skeleton key={i} className="h-[120px] w-full" />
            ))}
        </div>

        {/* Content library and details skeleton */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <Skeleton className="h-[600px] w-full lg:col-span-2" />
          <Skeleton className="h-[600px] w-full" />
        </div>
      </div>
    </DashboardLayout>
  )
}
