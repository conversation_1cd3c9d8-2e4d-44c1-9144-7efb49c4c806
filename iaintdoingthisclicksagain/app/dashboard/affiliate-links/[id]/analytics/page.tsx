import { WebSocketConfig } from "../../ws-config"
import { LinkAnalyticsView } from "@/components/dashboard/affiliate-links/link-analytics-view"

interface LinkAnalyticsPageProps {
  params: {
    id: string
  }
}

export default function LinkAnalyticsPage({ params }: LinkAnalyticsPageProps) {
  return (
    <div>
      <div className="absolute top-4 right-4 z-10">
        <WebSocketConfig />
      </div>
      <LinkAnalyticsView linkId={params.id} />
    </div>
  )
}
