import type { Metadata } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { AudienceHeader } from "@/components/dashboard/audience/audience-header"
import { AudienceOverview } from "@/components/dashboard/audience/audience-overview"
import { AudienceGrowth } from "@/components/dashboard/audience/audience-growth"
import { AudienceDemographics } from "@/components/dashboard/audience/audience-demographics"
import { AudienceInterests } from "@/components/dashboard/audience/audience-interests"
import { AudienceActivity } from "@/components/dashboard/audience/audience-activity"
import { AudienceSegments } from "@/components/dashboard/audience/audience-segments"

export const metadata: Metadata = {
  title: "Audience | Social Media Dashboard",
  description: "Comprehensive audience analytics and insights",
}

export default function AudiencePage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <AudienceHeader />
        <AudienceOverview />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <AudienceGrowth />
          <AudienceActivity />
        </div>
        <AudienceDemographics />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <AudienceInterests />
          <AudienceSegments />
        </div>
      </div>
    </DashboardLayout>
  )
}
