import type { Metadata } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { CampaignsHeader } from "@/components/dashboard/campaigns/campaigns-header"
import { CampaignMetrics } from "@/components/dashboard/campaigns/campaign-metrics"
import { CampaignsList } from "@/components/dashboard/campaigns/campaigns-list"
import { CampaignDetails } from "@/components/dashboard/campaigns/campaign-details"

export const metadata: Metadata = {
  title: "Campaigns | Social Media Dashboard",
  description: "Manage and analyze your social media campaigns",
}

export default function CampaignsPage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <CampaignsHeader />
        <CampaignMetrics />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <CampaignsList />
          </div>
          <div>
            <CampaignDetails />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
