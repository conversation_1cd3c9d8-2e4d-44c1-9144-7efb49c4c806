"use client"
import { useEffect, useRef, useState, useCallback } from "react"
import { toast } from "sonner"
import { logger } from '@/lib/debug/logger'
import { useDebugHook, useWebSocketDebug } from '@/lib/debug/useDebugValue'

// WebSocket connection states
export type ConnectionStatus = "connecting" | "connected" | "disconnected" | "error"

// Generic event payload for WebSocket messages
export interface WebSocketEvent<T = any> {
  type: string
  data: T
  timestamp: string
}

// Configuration for WebSocket client
export interface WebSocketConfig {
  url: string
  reconnectAttempts?: number
  reconnectInterval?: number
  onOpen?: (event: Event) => void
  onMessage?: (event: MessageEvent) => void
  onError?: (event: Event) => void
  onClose?: (event: CloseEvent) => void
  protocols?: string | string[]
  headers?: Record<string, string>
  autoReconnect?: boolean
}

/**
 * Custom hook for WebSocket connections with reconnection logic and status tracking
 */
export function useWebSocket<T = any>(config: WebSocketConfig) {
  const {
    url,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    onOpen,
    onMessage,
    onError,
    onClose,
    protocols,
    autoReconnect = true,
  } = config

  const [status, setStatus] = useState<ConnectionStatus>("disconnected")
  const [messages, setMessages] = useState<WebSocketEvent<T>[]>([])
  const [lastMessage, setLastMessage] = useState<WebSocketEvent<T> | null>(null)

  // Debug the WebSocket state
  useDebugHook(status, 'WebSocket Status', 'useWebSocket')
  useDebugHook(messages.length, 'Message Count', 'useWebSocket')

  const { logConnection, logMessage, logError } = useWebSocketDebug(url, 'useWebSocket')

  const wsRef = useRef<WebSocket | null>(null)
  const reconnectCountRef = useRef(0)
  const reconnectTimerRef = useRef<NodeJS.Timeout | null>(null)

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!url) {
      logger.error('WebSocket URL not provided', undefined, 'useWebSocket')
      return
    }

    try {
      setStatus("connecting")
      logger.debug('WebSocket connecting', { url, protocols }, 'useWebSocket')
      logConnection('connecting')

      // Close existing connection if any
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.close()
      }

      const ws = new WebSocket(url, protocols)

      ws.onopen = (event) => {
        setStatus("connected")
        reconnectCountRef.current = 0
        logger.info('WebSocket connected successfully', { url }, 'useWebSocket')
        logConnection('connected')
        toast.success("WebSocket connected", {
          description: "Receiving real-time data updates",
        })
        if (onOpen) onOpen(event)
      }

      ws.onmessage = (event) => {
        try {
          const parsedData: WebSocketEvent<T> = JSON.parse(event.data)
          logger.debug('WebSocket message received', parsedData, 'useWebSocket')
          logMessage(parsedData, 'incoming')
          setLastMessage(parsedData)
          setMessages((prev) => [parsedData, ...prev].slice(0, 100)) // Keep last 100 messages
          if (onMessage) onMessage(event)
        } catch (err) {
          logger.error('Error parsing WebSocket message', { error: err, data: event.data }, 'useWebSocket')
          logError(err)
        }
      }

      ws.onerror = (event) => {
        setStatus("error")
        logger.error('WebSocket error occurred', event, 'useWebSocket')
        logError(event)
        toast.error("WebSocket error", {
          description: "Connection error occurred",
        })
        if (onError) onError(event)
      }

      ws.onclose = (event) => {
        setStatus("disconnected")
        logger.warn('WebSocket connection closed', {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean
        }, 'useWebSocket')
        logConnection('disconnected')

        // Auto-reconnection logic with debugging
        if (autoReconnect && reconnectCountRef.current < reconnectAttempts && !event.wasClean) {
          reconnectCountRef.current++
          logger.info(`Attempting to reconnect (${reconnectCountRef.current}/${reconnectAttempts})`, undefined, 'useWebSocket')

          if (reconnectTimerRef.current) {
            clearTimeout(reconnectTimerRef.current)
          }

          toast.info(`Reconnecting (${reconnectCountRef.current}/${reconnectAttempts})...`, {
            description: `Attempting to reconnect in ${reconnectInterval / 1000}s`,
          })

          reconnectTimerRef.current = setTimeout(() => {
            connect()
          }, reconnectInterval)
        } else if (reconnectCountRef.current >= reconnectAttempts) {
          logger.error('Max reconnection attempts reached', undefined, 'useWebSocket')
          toast.error("WebSocket disconnected", {
            description: "Max reconnection attempts reached",
          })
        }

        if (onClose) onClose(event)
      }

      wsRef.current = ws
    } catch (error) {
      logger.error('Failed to create WebSocket connection', error, 'useWebSocket')
      logError(error)
      setStatus("error")
      toast.error("WebSocket connection failed", {
        description: error instanceof Error ? error.message : "Unknown error",
      })
    }
  }, [url, protocols, onOpen, onMessage, onError, onClose, autoReconnect, reconnectAttempts, reconnectInterval, logConnection, logMessage, logError])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }

    if (reconnectTimerRef.current) {
      clearTimeout(reconnectTimerRef.current)
      reconnectTimerRef.current = null
    }

    setStatus("disconnected")
  }, [])

  // Send message to WebSocket
  const sendMessage = useCallback((data: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      const message = typeof data === "string" ? data : JSON.stringify(data)
      wsRef.current.send(message)
      return true
    }
    return false
  }, [])

  // Connect/disconnect on mount/unmount
  useEffect(() => {
    connect()

    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  // Clear reconnect timer on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimerRef.current) {
        clearTimeout(reconnectTimerRef.current)
      }
    }
  }, [])

  return {
    status,
    messages,
    lastMessage,
    connect,
    disconnect,
    sendMessage,
    isConnected: status === "connected",
    isConnecting: status === "connecting",
    isDisconnected: status === "disconnected",
    isError: status === "error",
  }
}

/**
 * Creates a mock WebSocket server for development/testing
 */
export function createMockWebSocketServer(options: {
  interval?: number
  eventTypes?: string[]
  mockClickCallback?: (data: any) => void
}) {
  const { interval = 3000, eventTypes = ["click", "conversion", "pageview"], mockClickCallback } = options

  // Use mock URLs that won't actually connect for testing
  const mockWsUrl = "wss://mock-websocket-server.example/analytics"

  // Prepare mock message data
  const generateMockMessage = () => {
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    const isConversion = eventType === "conversion"

    const mockData = {
      id: Math.random().toString(36).substring(2, 15),
      linkId: `link_${Math.floor(Math.random() * 5) + 1}`,
      timestamp: new Date().toISOString(),
      country: ["United States", "United Kingdom", "Canada", "Australia", "Germany"][Math.floor(Math.random() * 5)],
      device: ["mobile", "desktop", "tablet"][Math.floor(Math.random() * 3)],
      referrer: ["Instagram", "Facebook", "Twitter", "Direct", "Email"][Math.floor(Math.random() * 5)],
      converted: isConversion,
      revenue: isConversion ? Math.round(Math.random() * 50 + 10) : undefined,
    }

    if (mockClickCallback) {
      mockClickCallback(mockData)
    }

    return {
      type: eventType,
      data: mockData,
      timestamp: new Date().toISOString(),
    }
  }

  return {
    url: mockWsUrl,
    generateMockMessage,
  }
}
