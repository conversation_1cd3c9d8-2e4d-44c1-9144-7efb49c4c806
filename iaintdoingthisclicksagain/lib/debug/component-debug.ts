import { useEffect, useRef } from 'react';
import { logger } from './logger';

// Store for component debug info
const componentDebugStore: Record<string, any> = {};

export function useComponentDebug(componentName: string, props: any = {}): void {
  // Only run in development
  if (process.env.NODE_ENV !== 'development') return;

  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());
  const renderTime = useRef(performance.now());

  // Track component lifecycle
  useEffect(() => {
    renderCount.current += 1;
    
    // Store component info
    componentDebugStore[componentName] = {
      name: componentName,
      renderCount: renderCount.current,
      mountTime: mountTime.current,
      lastRenderTime: Date.now(),
      props: { ...props }
    };

    logger.debug(
      `${componentName} rendered`,
      {
        renderCount: renderCount.current,
        renderTime: `${(performance.now() - renderTime.current).toFixed(2)}ms`
      },
      'ComponentDebug'
    );

    renderTime.current = performance.now();

    // Cleanup on unmount
    return () => {
      logger.debug(`${componentName} unmounted`, {
        totalLifetime: `${Date.now() - mountTime.current}ms`,
        renderCount: renderCount.current
      }, 'ComponentDebug');
      
      // Mark as unmounted but keep in store for debugging
      if (componentDebugStore[componentName]) {
        componentDebugStore[componentName].unmounted = true;
        componentDebugStore[componentName].unmountTime = Date.now();
      }
    };
  }, [componentName, ...Object.values(props)]);
}

export function getComponentDebugInfo(): Record<string, any> {
  return { ...componentDebugStore };
}

export function clearComponentDebugInfo(): void {
  Object.keys(componentDebugStore).forEach(key => {
    delete componentDebugStore[key];
  });
}