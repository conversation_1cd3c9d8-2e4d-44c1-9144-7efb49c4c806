"use client"
import { useEffect, useState } from 'react'
import { LucideIcon } from 'lucide-react'

interface IconProps {
  icon: LucideIcon
  className?: string
  size?: number
}

export function Icon({ icon: IconComponent, className, size }: IconProps) {
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    // Return a placeholder div with the same dimensions during SSR
    return (
      <div 
        className={className} 
        style={{ 
          width: size || 24, 
          height: size || 24,
          display: 'inline-block'
        }} 
      />
    )
  }

  return <IconComponent className={className} size={size} />
}
