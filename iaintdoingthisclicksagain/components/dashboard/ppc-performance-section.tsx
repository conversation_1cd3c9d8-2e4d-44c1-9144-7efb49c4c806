"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  ResponsiveContainer,
  Scatter,
  ScatterChart,
  Tooltip,
  XAxis,
  YAxis,
  ZAxis,
} from "recharts"
import { Progress } from "@/components/ui/progress"

const campaignData = [
  { name: "Summer Sale", ctr: 2.8, cpc: 1.2, size: 15000 },
  { name: "Product Launch", ctr: 3.5, cpc: 1.8, size: 25000 },
  { name: "Brand Awareness", ctr: 1.9, cpc: 0.9, size: 10000 },
  { name: "Retargeting", ctr: 4.2, cpc: 1.5, size: 8000 },
  { name: "Seasonal Promo", ctr: 3.1, cpc: 1.3, size: 12000 },
  { name: "New Audience", ctr: 2.3, cpc: 1.1, size: 18000 },
  { name: "<PERSON><PERSON><PERSON><PERSON>", ctr: 2.7, cpc: 1.4, size: 9000 },
  { name: "<PERSON>yal<PERSON>", ctr: 5.1, cpc: 1.7, size: 7000 },
]

const roasData = [
  { name: "Summer Sale", value: 3.2 },
  { name: "Product Launch", value: 2.8 },
  { name: "Brand Awareness", value: 1.5 },
  { name: "Retargeting", value: 5.4 },
  { name: "Seasonal Promo", value: 3.7 },
]

const budgetData = [
  { name: "Facebook", allocated: 5000, spent: 4200 },
  { name: "Instagram", allocated: 4000, spent: 3800 },
  { name: "LinkedIn", allocated: 3000, spent: 2100 },
  { name: "Twitter", allocated: 2000, spent: 1800 },
  { name: "YouTube", allocated: 1500, spent: 900 },
]

const anomalyData = [
  { date: "07/01", expected: 2.5, actual: 2.6 },
  { date: "07/02", expected: 2.6, actual: 2.5 },
  { date: "07/03", expected: 2.5, actual: 2.7 },
  { date: "07/04", expected: 2.6, actual: 2.5 },
  { date: "07/05", expected: 2.7, actual: 2.8 },
  { date: "07/06", expected: 2.6, actual: 2.5 },
  { date: "07/07", expected: 2.5, actual: 4.8 }, // Anomaly
  { date: "07/08", expected: 2.7, actual: 2.6 },
  { date: "07/09", expected: 2.6, actual: 2.5 },
  { date: "07/10", expected: 2.5, actual: 2.4 },
]

export function PPCPerformanceSection() {
  return (
    <Card className="col-span-1">
      <CardHeader>
        <CardTitle>PPC Performance</CardTitle>
        <CardDescription>Monitor your paid advertising performance metrics</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="campaigns">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="roas">ROAS</TabsTrigger>
            <TabsTrigger value="budget">Budget</TabsTrigger>
            <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          </TabsList>
          <TabsContent value="campaigns" className="pt-4">
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart
                  margin={{
                    top: 20,
                    right: 20,
                    bottom: 20,
                    left: 20,
                  }}
                >
                  <CartesianGrid />
                  <XAxis type="number" dataKey="ctr" name="CTR" unit="%" domain={[0, 6]} />
                  <YAxis type="number" dataKey="cpc" name="CPC" unit="$" domain={[0, 2]} />
                  <ZAxis type="number" dataKey="size" range={[50, 400]} name="Budget" unit="$" />
                  <Tooltip cursor={{ strokeDasharray: "3 3" }} />
                  <Legend />
                  <Scatter name="Campaigns" data={campaignData} fill="#4f46e5" shape="circle" />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-2 gap-4">
              <div className="rounded-lg border p-3">
                <div className="text-sm font-medium">Best Performing Campaign</div>
                <div className="text-lg font-bold">Retargeting</div>
                <div className="text-xs text-muted-foreground">CTR: 4.2% | CPC: $1.50</div>
              </div>
              <div className="rounded-lg border p-3">
                <div className="text-sm font-medium">Largest Campaign</div>
                <div className="text-lg font-bold">Product Launch</div>
                <div className="text-xs text-muted-foreground">Budget: $25,000 | CTR: 3.5%</div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="roas" className="pt-4">
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={roasData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 6]} />
                  <Tooltip formatter={(value) => [`${value}x`, "ROAS"]} />
                  <Legend />
                  <Bar dataKey="value" name="Return on Ad Spend">
                    {roasData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={entry.value >= 3 ? "#4ade80" : entry.value >= 2 ? "#a855f7" : "#ef4444"}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="rounded-lg border p-3 text-center">
                <div className="text-sm font-medium">Average ROAS</div>
                <div className="text-lg font-bold">3.3x</div>
                <div className="text-xs text-green-500">↑ 0.5x from last period</div>
              </div>
              <div className="rounded-lg border p-3 text-center">
                <div className="text-sm font-medium">Best Campaign</div>
                <div className="text-lg font-bold">Retargeting</div>
                <div className="text-xs text-green-500">5.4x ROAS</div>
              </div>
              <div className="rounded-lg border p-3 text-center">
                <div className="text-sm font-medium">Worst Campaign</div>
                <div className="text-lg font-bold">Brand Awareness</div>
                <div className="text-xs text-red-500">1.5x ROAS</div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="budget" className="pt-4">
            <div className="space-y-4">
              {budgetData.map((item, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">{item.name}</div>
                    <div className="text-sm text-muted-foreground">
                      ${item.spent.toLocaleString()} / ${item.allocated.toLocaleString()}
                    </div>
                  </div>
                  <Progress value={(item.spent / item.allocated) * 100} className="h-2" />
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div>{Math.round((item.spent / item.allocated) * 100)}% spent</div>
                    <div>
                      {item.spent < item.allocated
                        ? `$${(item.allocated - item.spent).toLocaleString()} remaining`
                        : "Budget depleted"}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 grid grid-cols-2 gap-4">
              <div className="rounded-lg border p-3">
                <div className="text-sm font-medium">Total Budget</div>
                <div className="text-lg font-bold">$15,500</div>
                <div className="text-xs text-muted-foreground">$12,800 spent (82.6%)</div>
              </div>
              <div className="rounded-lg border p-3">
                <div className="text-sm font-medium">Pacing</div>
                <div className="text-lg font-bold">On Track</div>
                <div className="text-xs text-muted-foreground">17.4% budget remaining, 18% time remaining</div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="anomalies" className="pt-4">
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={anomalyData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis domain={[0, 5]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="expected" name="Expected CTR (%)" fill="#8b5cf6" />
                  <Bar dataKey="actual" name="Actual CTR (%)">
                    {anomalyData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={Math.abs(entry.actual - entry.expected) > 1 ? "#ef4444" : "#4ade80"}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-4 rounded-lg border border-red-200 bg-red-50 p-3 dark:border-red-900 dark:bg-red-950/30">
              <div className="text-sm font-medium text-red-600 dark:text-red-400">Anomaly Detected</div>
              <div className="text-xs text-red-600 dark:text-red-400">
                Unusual spike in CTR on July 7th (4.8% vs expected 2.5%). This could indicate click fraud or a highly
                successful ad variation. We recommend investigating this campaign.
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
