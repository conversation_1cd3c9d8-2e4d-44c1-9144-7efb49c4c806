"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>ertCircle, ArrowUpRight, Lightbulb, TrendingDown, TrendingUp } from "lucide-react"
import { Badge } from "@/components/ui/badge"

export function AutomatedInsights() {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Lightbulb className="h-5 w-5 text-yellow-500" />
            <CardTitle>Automated Insights</CardTitle>
          </div>
          <Badge variant="outline" className="px-2 py-1">
            Updated 2 hours ago
          </Badge>
        </div>
        <CardDescription>AI-powered insights based on your social media performance</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Engagement Spike</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-500" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-1">
              <p className="text-sm text-muted-foreground">
                Your Instagram post about product updates received 215% more engagement than your average post. Consider
                creating more product-focused content.
              </p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-red-500">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Budget Alert</CardTitle>
                <AlertCircle className="h-4 w-4 text-red-500" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-1">
              <p className="text-sm text-muted-foreground">
                Your Facebook ad campaign "Summer Sale" is spending 30% above daily budget. Consider adjusting bid
                strategy or audience targeting.
              </p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Audience Growth</CardTitle>
                <ArrowUpRight className="h-4 w-4 text-blue-500" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-1">
              <p className="text-sm text-muted-foreground">
                Your LinkedIn follower count has grown by 12% this month, outpacing your industry benchmark of 3%. Your
                thought leadership content is resonating.
              </p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-yellow-500">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Content Opportunity</CardTitle>
                <Lightbulb className="h-4 w-4 text-yellow-500" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-1">
              <p className="text-sm text-muted-foreground">
                Videos under 60 seconds are generating 3x more engagement than longer content. Consider creating more
                short-form video content.
              </p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Competitor Analysis</CardTitle>
                <TrendingUp className="h-4 w-4 text-purple-500" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-1">
              <p className="text-sm text-muted-foreground">
                Your main competitor has increased posting frequency by 40% on Twitter. Their engagement rate has
                improved by 15% as a result.
              </p>
            </CardContent>
          </Card>
          <Card className="border-l-4 border-l-red-500">
            <CardHeader className="p-4 pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium">Conversion Drop</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-500" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-1">
              <p className="text-sm text-muted-foreground">
                Your landing page conversion rate has dropped by 8% in the last week. Check for technical issues or
                consider refreshing your call-to-action.
              </p>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  )
}
