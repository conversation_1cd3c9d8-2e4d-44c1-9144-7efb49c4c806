"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Calendar,
  Edit,
  Eye,
  MoreHorizontal,
  Search,
  Trash2,
  CalendarIcon,
  ThumbsUp,
  MessageSquare,
  Share2,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ContentDetails } from "./content-details"

const contentData = [
  {
    id: 1,
    title: "10 Tips for Better Social Media Marketing",
    type: "Article",
    platform: "LinkedIn",
    status: "Published",
    publishDate: "2023-07-15",
    likes: 245,
    comments: 32,
    shares: 78,
    views: 1250,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 2,
    title: "New Product Launch Announcement",
    type: "Image",
    platform: "Facebook",
    status: "Published",
    publishDate: "2023-07-10",
    likes: 189,
    comments: 24,
    shares: 45,
    views: 980,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 3,
    title: "Behind the Scenes: Our Team at Work",
    type: "Video",
    platform: "Instagram",
    status: "Published",
    publishDate: "2023-07-05",
    likes: 320,
    comments: 56,
    shares: 89,
    views: 1500,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 4,
    title: "Customer Success Story: ABC Corp",
    type: "Link",
    platform: "Twitter",
    status: "Published",
    publishDate: "2023-07-01",
    likes: 156,
    comments: 18,
    shares: 34,
    views: 750,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 5,
    title: "Industry Trends for 2023",
    type: "Article",
    platform: "LinkedIn",
    status: "Published",
    publishDate: "2023-06-28",
    likes: 210,
    comments: 28,
    shares: 65,
    views: 1100,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 6,
    title: "Holiday Special Promotion",
    type: "Image",
    platform: "Instagram",
    status: "Scheduled",
    publishDate: "2023-12-01",
    likes: 0,
    comments: 0,
    shares: 0,
    views: 0,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 7,
    title: "End of Year Sale Announcement",
    type: "Video",
    platform: "Facebook",
    status: "Scheduled",
    publishDate: "2023-11-25",
    likes: 0,
    comments: 0,
    shares: 0,
    views: 0,
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: 8,
    title: "Product Feature Highlight",
    type: "Image",
    platform: "Twitter",
    status: "Draft",
    publishDate: "",
    likes: 0,
    comments: 0,
    shares: 0,
    views: 0,
    image: "/placeholder.svg?height=100&width=100",
  },
]

export function ContentLibrary({ onSelectContent }: { onSelectContent?: (content: any) => void }) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedContent, setSelectedContent] = useState<any>(null)
  const [viewDetailsOpen, setViewDetailsOpen] = useState(false)
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterType, setFilterType] = useState("all")
  const [filterPlatform, setFilterPlatform] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Filter content based on search query and filters
  const filteredContent = contentData.filter((content) => {
    const matchesSearch = content.title.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = filterStatus === "all" || content.status === filterStatus
    const matchesType = filterType === "all" || content.type === filterType
    const matchesPlatform = filterPlatform === "all" || content.platform === filterPlatform
    return matchesSearch && matchesStatus && matchesType && matchesPlatform
  })

  const handleViewDetails = (content: any) => {
    setSelectedContent(content)
    setViewDetailsOpen(true)
    if (onSelectContent) {
      onSelectContent(content)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Content Library</CardTitle>
            <CardDescription>Manage and analyze your social media content</CardDescription>
          </div>
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search content..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="space-y-4">
          <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
            <TabsList>
              <TabsTrigger value="all" onClick={() => setFilterStatus("all")}>
                All
              </TabsTrigger>
              <TabsTrigger value="published" onClick={() => setFilterStatus("Published")}>
                Published
              </TabsTrigger>
              <TabsTrigger value="scheduled" onClick={() => setFilterStatus("Scheduled")}>
                Scheduled
              </TabsTrigger>
              <TabsTrigger value="draft" onClick={() => setFilterStatus("Draft")}>
                Drafts
              </TabsTrigger>
            </TabsList>
            <div className="flex flex-wrap gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Content Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Image">Image</SelectItem>
                  <SelectItem value="Video">Video</SelectItem>
                  <SelectItem value="Article">Article</SelectItem>
                  <SelectItem value="Link">Link</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterPlatform} onValueChange={setFilterPlatform}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Platforms</SelectItem>
                  <SelectItem value="Facebook">Facebook</SelectItem>
                  <SelectItem value="Instagram">Instagram</SelectItem>
                  <SelectItem value="Twitter">Twitter</SelectItem>
                  <SelectItem value="LinkedIn">LinkedIn</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex rounded-md border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-r-none"
                  onClick={() => setViewMode("grid")}
                >
                  Grid
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  className="rounded-l-none"
                  onClick={() => setViewMode("list")}
                >
                  List
                </Button>
              </div>
            </div>
          </div>

          <TabsContent value="all" className="space-y-4">
            {viewMode === "grid" ? (
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {filteredContent.map((content) => (
                  <Card key={content.id} className="overflow-hidden">
                    <div className="relative aspect-video w-full overflow-hidden bg-muted">
                      <img
                        src={content.image || "/placeholder.svg"}
                        alt={content.title}
                        className="h-full w-full object-cover transition-all hover:scale-105"
                      />
                      <div className="absolute right-2 top-2">
                        <Badge
                          variant={
                            content.status === "Published"
                              ? "default"
                              : content.status === "Scheduled"
                                ? "secondary"
                                : "outline"
                          }
                        >
                          {content.status}
                        </Badge>
                      </div>
                    </div>
                    <CardHeader className="p-3">
                      <div className="flex items-center justify-between">
                        <Badge variant="outline">{content.platform}</Badge>
                        <Badge variant="outline">{content.type}</Badge>
                      </div>
                      <CardTitle className="line-clamp-1 text-base">{content.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="flex items-center justify-between p-3 pt-0">
                      <div className="flex items-center text-sm text-muted-foreground">
                        {content.status === "Published" ? (
                          <>
                            <Eye className="mr-1 h-4 w-4" />
                            {content.views.toLocaleString()}
                          </>
                        ) : content.status === "Scheduled" ? (
                          <>
                            <CalendarIcon className="mr-1 h-4 w-4" />
                            {new Date(content.publishDate).toLocaleDateString()}
                          </>
                        ) : (
                          <>
                            <Edit className="mr-1 h-4 w-4" />
                            Draft
                          </>
                        )}
                      </div>
                      <Button variant="ghost" size="sm" onClick={() => handleViewDetails(content)}>
                        View
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">Title</th>
                      <th className="p-2 text-left font-medium">Type</th>
                      <th className="p-2 text-left font-medium">Platform</th>
                      <th className="p-2 text-left font-medium">Status</th>
                      <th className="p-2 text-left font-medium">Date</th>
                      <th className="p-2 text-left font-medium">Engagement</th>
                      <th className="p-2 text-left font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredContent.map((content) => (
                      <tr key={content.id} className="border-b">
                        <td className="p-2">
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={content.image || "/placeholder.svg"} alt={content.title} />
                              <AvatarFallback>{content.title.substring(0, 2)}</AvatarFallback>
                            </Avatar>
                            <span className="line-clamp-1 font-medium">{content.title}</span>
                          </div>
                        </td>
                        <td className="p-2">{content.type}</td>
                        <td className="p-2">
                          <Badge variant="outline">{content.platform}</Badge>
                        </td>
                        <td className="p-2">
                          <Badge
                            variant={
                              content.status === "Published"
                                ? "default"
                                : content.status === "Scheduled"
                                  ? "secondary"
                                  : "outline"
                            }
                          >
                            {content.status}
                          </Badge>
                        </td>
                        <td className="p-2">
                          {content.publishDate ? new Date(content.publishDate).toLocaleDateString() : "-"}
                        </td>
                        <td className="p-2">
                          {content.status === "Published" ? (
                            <div className="flex items-center gap-2">
                              <div className="flex items-center">
                                <ThumbsUp className="mr-1 h-3 w-3" />
                                {content.likes}
                              </div>
                              <div className="flex items-center">
                                <MessageSquare className="mr-1 h-3 w-3" />
                                {content.comments}
                              </div>
                              <div className="flex items-center">
                                <Share2 className="mr-1 h-3 w-3" />
                                {content.shares}
                              </div>
                            </div>
                          ) : (
                            "-"
                          )}
                        </td>
                        <td className="p-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewDetails(content)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              {content.status === "Scheduled" && (
                                <DropdownMenuItem>
                                  <Calendar className="mr-2 h-4 w-4" />
                                  Reschedule
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>

      <Dialog open={viewDetailsOpen} onOpenChange={setViewDetailsOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Content Details</DialogTitle>
            <DialogDescription>Detailed information about the content</DialogDescription>
          </DialogHeader>
          {selectedContent && <ContentDetails content={selectedContent} />}
          <DialogFooter>
            <Button variant="outline" onClick={() => setViewDetailsOpen(false)}>
              Close
            </Button>
            <Button>Edit Content</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
