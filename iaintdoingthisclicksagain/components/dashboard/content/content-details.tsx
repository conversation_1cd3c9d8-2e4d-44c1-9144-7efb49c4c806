"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"
import { CalendarIcon, ThumbsUp, MessageSquare, Share2, Eye } from "lucide-react"

// Sample data for charts
const dailyData = [
  { name: "Day 1", views: 250, likes: 50, comments: 10, shares: 5 },
  { name: "Day 2", views: 300, likes: 60, comments: 12, shares: 8 },
  { name: "Day 3", views: 350, likes: 70, comments: 14, shares: 10 },
  { name: "Day 4", views: 400, likes: 80, comments: 16, shares: 12 },
  { name: "Day 5", views: 375, likes: 75, comments: 15, shares: 11 },
  { name: "Day 6", views: 450, likes: 90, comments: 18, shares: 14 },
  { name: "Day 7", views: 500, likes: 100, comments: 20, shares: 16 },
]

const demographicData = [
  { name: "18-24", value: 25 },
  { name: "25-34", value: 35 },
  { name: "35-44", value: 20 },
  { name: "45-54", value: 10 },
  { name: "55+", value: 10 },
]

const COLORS = ["#4f46e5", "#8b5cf6", "#a855f7", "#d946ef", "#ec4899"]

// Add a default content object at the top of the file
const defaultContent = {
  id: 1,
  title: "Select content to view details",
  status: "Published",
  platform: "All Platforms",
  type: "Overview",
  publishDate: new Date().toISOString(),
  image: "/placeholder.svg?height=400&width=600",
  likes: 0,
  comments: 0,
  shares: 0,
  views: 0,
  clicks: 0,
}

// Add a Card wrapper around the content when no specific content is selected
export function ContentDetails({ content }: { content?: any }) {
  // If content is null, undefined, or the default content, show a placeholder card
  if (!content) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Content Details</CardTitle>
          <CardDescription>Select content from the library to view details</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center p-6 text-center">
          <div className="mb-4 rounded-lg bg-muted p-8">
            <Eye className="h-12 w-12 text-muted-foreground" />
          </div>
          <p className="text-muted-foreground">
            Click on any content item in the library to view detailed analytics and information
          </p>
        </CardContent>
      </Card>
    )
  }

  // Rest of the component for when content is provided
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        <div>
          <div className="aspect-video w-full overflow-hidden rounded-lg bg-muted">
            <img src={content.image || "/placeholder.svg"} alt={content.title} className="h-full w-full object-cover" />
          </div>
        </div>
        <div>
          <h3 className="text-lg font-medium">{content.title}</h3>
          <div className="mt-2 flex items-center gap-2">
            <Badge
              variant={
                content.status === "Published" ? "default" : content.status === "Scheduled" ? "secondary" : "outline"
              }
            >
              {content.status}
            </Badge>
            <Badge variant="outline">{content.platform}</Badge>
            <Badge variant="outline">{content.type}</Badge>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">
                {content.publishDate ? new Date(content.publishDate).toLocaleDateString() : "Not published"}
              </span>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-4 gap-4">
            <Card className="p-2 text-center">
              <div className="flex flex-col items-center">
                <ThumbsUp className="mb-1 h-4 w-4 text-muted-foreground" />
                <span className="text-lg font-bold">{content.likes}</span>
                <span className="text-xs text-muted-foreground">Likes</span>
              </div>
            </Card>
            <Card className="p-2 text-center">
              <div className="flex flex-col items-center">
                <MessageSquare className="mb-1 h-4 w-4 text-muted-foreground" />
                <span className="text-lg font-bold">{content.comments}</span>
                <span className="text-xs text-muted-foreground">Comments</span>
              </div>
            </Card>
            <Card className="p-2 text-center">
              <div className="flex flex-col items-center">
                <Share2 className="mb-1 h-4 w-4 text-muted-foreground" />
                <span className="text-lg font-bold">{content.shares}</span>
                <span className="text-xs text-muted-foreground">Shares</span>
              </div>
            </Card>
            <Card className="p-2 text-center">
              <div className="flex flex-col items-center">
                <Eye className="mb-1 h-4 w-4 text-muted-foreground" />
                <span className="text-lg font-bold">{content.views}</span>
                <span className="text-xs text-muted-foreground">Views</span>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {content.status === "Published" && (
        <Tabs defaultValue="performance" className="space-y-4">
          <TabsList>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="audience">Audience</TabsTrigger>
            <TabsTrigger value="engagement">Engagement</TabsTrigger>
          </TabsList>
          <TabsContent value="performance" className="space-y-4">
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={dailyData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="views" stroke="#4f46e5" name="Views" />
                  <Line type="monotone" dataKey="likes" stroke="#a855f7" name="Likes" />
                  <Line type="monotone" dataKey="comments" stroke="#ec4899" name="Comments" />
                  <Line type="monotone" dataKey="shares" stroke="#f43f5e" name="Shares" />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Engagement Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {content.views > 0
                      ? (((content.likes + content.comments + content.shares) / content.views) * 100).toFixed(2)
                      : "0.00"}
                    %
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Click-Through Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {content.views > 0 ? (((content.clicks || 0) / content.views) * 100).toFixed(2) : "0.00"}%
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Virality Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {content.views > 0 ? ((content.shares / content.views) * 100).toFixed(2) : "0.00"}%
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="audience" className="space-y-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <h3 className="mb-4 text-lg font-medium">Age Distribution</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={demographicData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {demographicData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value}%`, "Percentage"]} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
              <div>
                <h3 className="mb-4 text-lg font-medium">Engagement by Age Group</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={demographicData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="value" name="Engagement %" fill="#4f46e5" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="engagement" className="space-y-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Engagement Breakdown</CardTitle>
                  <CardDescription>How users are engaging with your content</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={[
                            { name: "Likes", value: content.likes },
                            { name: "Comments", value: content.comments },
                            { name: "Shares", value: content.shares },
                          ]}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={100}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          <Cell fill="#4f46e5" />
                          <Cell fill="#a855f7" />
                          <Cell fill="#ec4899" />
                        </Pie>
                        <Tooltip formatter={(value) => [value, "Count"]} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Engagement Over Time</CardTitle>
                  <CardDescription>How engagement has changed over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[300px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={dailyData}
                        margin={{
                          top: 5,
                          right: 30,
                          left: 20,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="likes" name="Likes" fill="#4f46e5" />
                        <Bar dataKey="comments" name="Comments" fill="#a855f7" />
                        <Bar dataKey="shares" name="Shares" fill="#ec4899" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}
