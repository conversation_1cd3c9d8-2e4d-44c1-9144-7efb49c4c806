"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>p, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Eye, Heart, <PERSON>hare2, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

export function PerformanceOverview() {
  const metrics = [
    {
      title: "Total Reach",
      value: "2.4M",
      change: "+18.2%",
      trend: "up",
      icon: Eye,
      target: 85,
      description: "People reached this period",
    },
    {
      title: "Engagement Rate",
      value: "4.2%",
      change: "+0.8%",
      trend: "up",
      icon: Heart,
      target: 92,
      description: "Average engagement across platforms",
    },
    {
      title: "Click-Through Rate",
      value: "2.1%",
      change: "-0.3%",
      trend: "down",
      icon: MousePointer,
      target: 68,
      description: "Clicks per impression",
    },
    {
      title: "Follower Growth",
      value: "+12.5K",
      change: "+24.1%",
      trend: "up",
      icon: Users,
      target: 78,
      description: "New followers this period",
    },
    {
      title: "Share Rate",
      value: "1.8%",
      change: "+0.5%",
      trend: "up",
      icon: Share2,
      target: 88,
      description: "Content shared by audience",
    },
    {
      title: "Performance Score",
      value: "87/100",
      change: "+5 pts",
      trend: "up",
      icon: TrendingUp,
      target: 87,
      description: "Overall performance rating",
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center text-xs">
              {metric.trend === "up" ? (
                <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
              )}
              <span className={metric.trend === "up" ? "text-emerald-500" : "text-rose-500"}>{metric.change}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{metric.description}</span>
                <span>{metric.target}%</span>
              </div>
              <Progress value={metric.target} className="mt-1" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
