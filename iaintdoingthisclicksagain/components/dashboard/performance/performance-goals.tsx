"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Target, TrendingUp, Calendar } from "lucide-react"

export function PerformanceGoals() {
  const goals = [
    {
      title: "Monthly Reach Target",
      current: 2400000,
      target: 3000000,
      progress: 80,
      status: "on-track",
      deadline: "End of Month",
      icon: Target,
    },
    {
      title: "Engagement Rate Goal",
      current: 4.2,
      target: 5.0,
      progress: 84,
      status: "on-track",
      deadline: "Quarterly",
      icon: TrendingUp,
    },
    {
      title: "Follower Growth",
      current: 12500,
      target: 15000,
      progress: 83,
      status: "on-track",
      deadline: "End of Quarter",
      icon: TrendingUp,
    },
    {
      title: "Content Publishing",
      current: 28,
      target: 30,
      progress: 93,
      status: "ahead",
      deadline: "Monthly",
      icon: Calendar,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ahead":
        return "bg-emerald-500"
      case "on-track":
        return "bg-blue-500"
      case "behind":
        return "bg-orange-500"
      case "at-risk":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "ahead":
        return "Ahead of Schedule"
      case "on-track":
        return "On Track"
      case "behind":
        return "Behind Schedule"
      case "at-risk":
        return "At Risk"
      default:
        return "Unknown"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Goals</CardTitle>
        <CardDescription>Track progress towards your objectives</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {goals.map((goal, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <goal.icon className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{goal.title}</span>
                </div>
                <Badge variant="outline" className={`${getStatusColor(goal.status)} text-white`}>
                  {getStatusText(goal.status)}
                </Badge>
              </div>
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>
                  {typeof goal.current === "number" && goal.current > 1000
                    ? goal.current.toLocaleString()
                    : goal.current}
                  {typeof goal.target === "number" && goal.target > 1000 ? "" : "%"} of{" "}
                  {typeof goal.target === "number" && goal.target > 1000 ? goal.target.toLocaleString() : goal.target}
                  {typeof goal.target === "number" && goal.target > 1000 ? "" : "%"}
                </span>
                <span>{goal.deadline}</span>
              </div>
              <Progress value={goal.progress} className="h-2" />
              <div className="text-right text-xs text-muted-foreground">{goal.progress}% complete</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
