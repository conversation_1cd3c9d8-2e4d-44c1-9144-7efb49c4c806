"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { DateRangeSelector } from "@/components/dashboard/date-range-selector"
import { PlatformSelector } from "@/components/dashboard/platform-selector"
import { Download, RefreshCw, Settings } from "lucide-react"

export function PerformanceHeader() {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Performance Analytics</h1>
        <p className="text-muted-foreground">Track and analyze your social media performance metrics</p>
      </div>
      <div className="flex flex-col gap-2 sm:flex-row sm:items-center">
        <DateRangeSelector />
        <PlatformSelector />
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Configure
          </Button>
        </div>
      </div>
    </div>
  )
}
