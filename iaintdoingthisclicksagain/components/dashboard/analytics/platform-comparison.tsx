"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, <PERSON>A<PERSON><PERSON> } from "recharts"
import { ChartTooltip } from "@/components/ui/chart"

const platformData = [
  {
    name: "Jan",
    facebook: 4000,
    instagram: 2400,
    twitter: 2400,
    linkedin: 1800,
  },
  {
    name: "Feb",
    facebook: 3000,
    instagram: 1398,
    twitter: 2210,
    linkedin: 1600,
  },
  {
    name: "Mar",
    facebook: 2000,
    instagram: 9800,
    twitter: 2290,
    linkedin: 2100,
  },
  {
    name: "Apr",
    facebook: 2780,
    instagram: 3908,
    twitter: 2000,
    linkedin: 2400,
  },
  {
    name: "May",
    facebook: 1890,
    instagram: 4800,
    twitter: 2181,
    linkedin: 2200,
  },
  {
    name: "Jun",
    facebook: 2390,
    instagram: 3800,
    twitter: 2500,
    linkedin: 2300,
  },
  {
    name: "Jul",
    facebook: 3490,
    instagram: 4300,
    twitter: 2100,
    linkedin: 2500,
  },
]

export function PlatformComparison() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Comparison</CardTitle>
        <CardDescription>Compare performance across social media platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={platformData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip content={<ChartTooltip />} />
            <Legend />
            <Bar dataKey="facebook" name="Facebook" fill="#4267B2" radius={[4, 4, 0, 0]} />
            <Bar dataKey="instagram" name="Instagram" fill="#E1306C" radius={[4, 4, 0, 0]} />
            <Bar dataKey="twitter" name="Twitter" fill="#1DA1F2" radius={[4, 4, 0, 0]} />
            <Bar dataKey="linkedin" name="LinkedIn" fill="#0077B5" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  )
}
