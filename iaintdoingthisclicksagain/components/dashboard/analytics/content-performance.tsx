"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ArrowUpRight, BarChart2, Eye, Heart, MessageSquare, Share2 } from "lucide-react"

const contentData = [
  {
    id: 1,
    title: "10 Tips for Social Media Success",
    type: "Blog Post",
    platform: "Facebook",
    date: "2023-05-15",
    impressions: 12500,
    engagement: 3.8,
    clicks: 820,
    likes: 350,
    comments: 45,
    shares: 120,
  },
  {
    id: 2,
    title: "Product Launch Announcement",
    type: "Video",
    platform: "Instagram",
    date: "2023-05-20",
    impressions: 18900,
    engagement: 5.2,
    clicks: 1250,
    likes: 890,
    comments: 76,
    shares: 210,
  },
  {
    id: 3,
    title: "Customer Success Story",
    type: "Image",
    platform: "LinkedIn",
    date: "2023-05-22",
    impressions: 8700,
    engagement: 4.1,
    clicks: 620,
    likes: 230,
    comments: 28,
    shares: 85,
  },
  {
    id: 4,
    title: "Industry News Update",
    type: "Link",
    platform: "Twitter",
    date: "2023-05-25",
    impressions: 6500,
    engagement: 2.9,
    clicks: 480,
    likes: 180,
    comments: 22,
    shares: 65,
  },
  {
    id: 5,
    title: "Behind the Scenes",
    type: "Story",
    platform: "Instagram",
    date: "2023-05-28",
    impressions: 9800,
    engagement: 4.5,
    clicks: 720,
    likes: 410,
    comments: 38,
    shares: 95,
  },
]

export function ContentPerformance() {
  const [sortBy, setSortBy] = useState("engagement")

  const sortedContent = [...contentData].sort((a, b) => {
    if (sortBy === "engagement") return b.engagement - a.engagement
    if (sortBy === "impressions") return b.impressions - a.impressions
    if (sortBy === "clicks") return b.clicks - a.clicks
    return 0
  })

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Content Performance</CardTitle>
            <CardDescription>Analyze your top performing content</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Sort by:</span>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="engagement">Engagement Rate</SelectItem>
                <SelectItem value="impressions">Impressions</SelectItem>
                <SelectItem value="clicks">Clicks</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Content</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Eye className="mr-1 h-4 w-4" />
                    Impressions
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <BarChart2 className="mr-1 h-4 w-4" />
                    Engagement
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <ArrowUpRight className="mr-1 h-4 w-4" />
                    Clicks
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Heart className="mr-1 h-4 w-4" />
                    Likes
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <MessageSquare className="mr-1 h-4 w-4" />
                    Comments
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Share2 className="mr-1 h-4 w-4" />
                    Shares
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedContent.map((content) => (
                <TableRow key={content.id}>
                  <TableCell>
                    <div className="font-medium">{content.title}</div>
                    <div className="text-sm text-muted-foreground">
                      <Badge variant="outline" className="mr-1">
                        {content.type}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{content.platform}</TableCell>
                  <TableCell>{content.date}</TableCell>
                  <TableCell className="text-right">{content.impressions.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.engagement}%</TableCell>
                  <TableCell className="text-right">{content.clicks.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.likes.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.comments.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.shares.toLocaleString()}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
