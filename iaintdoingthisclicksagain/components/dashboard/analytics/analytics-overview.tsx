"use client"

import { <PERSON><PERSON><PERSON>, ArrowUp, <PERSON>, <PERSON>, <PERSON>hare2, Heart } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

export function AnalyticsOverview() {
  const metrics = [
    {
      title: "Total Followers",
      value: "124,892",
      change: "+12.3%",
      trend: "up",
      icon: Users,
    },
    {
      title: "Total Impressions",
      value: "1.2M",
      change: "+8.7%",
      trend: "up",
      icon: Eye,
    },
    {
      title: "Engagement Rate",
      value: "3.8%",
      change: "-0.5%",
      trend: "down",
      icon: Heart,
    },
    {
      title: "Total Shares",
      value: "24,521",
      change: "+15.2%",
      trend: "up",
      icon: Share2,
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center text-xs">
              {metric.trend === "up" ? (
                <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
              )}
              <span className={metric.trend === "up" ? "text-emerald-500" : "text-rose-500"}>{metric.change}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
