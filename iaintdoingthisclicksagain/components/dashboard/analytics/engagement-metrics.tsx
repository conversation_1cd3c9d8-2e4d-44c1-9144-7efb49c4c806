"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts"
import { ChartTooltip } from "@/components/ui/chart"

const engagementData = [
  {
    name: "Jan",
    likes: 4000,
    comments: 2400,
    shares: 1200,
  },
  {
    name: "Feb",
    likes: 3000,
    comments: 1398,
    shares: 900,
  },
  {
    name: "Mar",
    likes: 2000,
    comments: 9800,
    shares: 1800,
  },
  {
    name: "Apr",
    likes: 2780,
    comments: 3908,
    shares: 2500,
  },
  {
    name: "May",
    likes: 1890,
    comments: 4800,
    shares: 2300,
  },
  {
    name: "Jun",
    likes: 2390,
    comments: 3800,
    shares: 2100,
  },
  {
    name: "Jul",
    likes: 3490,
    comments: 4300,
    shares: 2400,
  },
]

export function EngagementMetrics() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <Card>
      <CardHeader>
        <CardTitle>Engagement Metrics</CardTitle>
        <CardDescription>Track likes, comments, and shares across platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="likes">Likes</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
            <TabsTrigger value="shares">Shares</TabsTrigger>
          </TabsList>
          <TabsContent value="overview">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Legend />
                <Bar dataKey="likes" name="Likes" fill="#8884d8" radius={[4, 4, 0, 0]} />
                <Bar dataKey="comments" name="Comments" fill="#82ca9d" radius={[4, 4, 0, 0]} />
                <Bar dataKey="shares" name="Shares" fill="#ffc658" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="likes">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Area type="monotone" dataKey="likes" name="Likes" stroke="#8884d8" fill="#8884d8" fillOpacity={0.3} />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="comments">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Area
                  type="monotone"
                  dataKey="comments"
                  name="Comments"
                  stroke="#82ca9d"
                  fill="#82ca9d"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="shares">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={engagementData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip content={<ChartTooltip />} />
                <Area
                  type="monotone"
                  dataKey="shares"
                  name="Shares"
                  stroke="#ffc658"
                  fill="#ffc658"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
