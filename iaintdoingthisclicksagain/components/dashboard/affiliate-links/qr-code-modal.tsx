"use client"
import { useState, useEffect } from "react"
import { QRCodeSVG } from "qrcode.react"
import { CheckIcon, CopyIcon, DownloadIcon, Share2Icon } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface QRCodeModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  link: string
  campaign: string
}

export function QRCodeModal({ open, onOpenChange, link, campaign }: QRCodeModalProps) {
  const [qrSize, setQrSize] = useState<number>(200)
  const [qrColor, setQrColor] = useState<string>("#000000")
  const [qrBgColor, setQrBgColor] = useState<string>("#FFFFFF")
  const [qrLevel, setQrLevel] = useState<"L" | "M" | "Q" | "H">("M")
  const [copied, setCopied] = useState<boolean>(false)

  // Reset copied state when modal closes
  useEffect(() => {
    if (!open) {
      setCopied(false)
    }
  }, [open])

  // Handle copy link
  const handleCopyLink = () => {
    navigator.clipboard.writeText(link)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
    toast.success("Link copied to clipboard")
  }

  // Handle download QR code
  const handleDownloadQR = () => {
    const svg = document.getElementById("qr-code-svg")
    if (!svg) return

    const svgData = new XMLSerializer().serializeToString(svg)
    const canvas = document.createElement("canvas")
    const ctx = canvas.getContext("2d")
    const img = new Image()

    img.onload = () => {
      canvas.width = qrSize
      canvas.height = qrSize
      ctx?.drawImage(img, 0, 0)
      const pngFile = canvas.toDataURL("image/png")

      const downloadLink = document.createElement("a")
      downloadLink.download = `${campaign.replace(/\s+/g, "-").toLowerCase()}-qr-code.png`
      downloadLink.href = pngFile
      downloadLink.click()

      toast.success("QR code downloaded")
    }

    img.src = "data:image/svg+xml;base64," + btoa(svgData)
  }

  // Handle share QR code
  const handleShareQR = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `QR Code for ${campaign}`,
          text: `Scan this QR code to access ${campaign}`,
          url: link,
        })
        toast.success("QR code shared successfully")
      } catch (error) {
        toast.error("Error sharing QR code")
      }
    } else {
      toast.error("Web Share API not supported in your browser")
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>QR Code Generator</DialogTitle>
          <DialogDescription>
            Generate a QR code for your affiliate link that can be downloaded or shared.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="preview" className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="customize">Customize</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="flex flex-col items-center justify-center space-y-4 pt-4">
            <div
              className="flex items-center justify-center rounded-lg border p-4"
              style={{ backgroundColor: qrBgColor }}
            >
              <QRCodeSVG
                id="qr-code-svg"
                value={link}
                size={qrSize}
                bgColor={qrBgColor}
                fgColor={qrColor}
                level={qrLevel}
                includeMargin
              />
            </div>

            <div className="w-full space-y-2">
              <Label htmlFor="qr-link">Affiliate Link</Label>
              <div className="flex items-center space-x-2">
                <Input id="qr-link" value={link} readOnly className="font-mono text-sm" />
                <Button variant="outline" size="icon" onClick={handleCopyLink} className="shrink-0">
                  {copied ? <CheckIcon className="h-4 w-4 text-green-500" /> : <CopyIcon className="h-4 w-4" />}
                  <span className="sr-only">Copy link</span>
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="customize" className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="qr-size">Size</Label>
                <Select value={qrSize.toString()} onValueChange={(value) => setQrSize(Number.parseInt(value))}>
                  <SelectTrigger id="qr-size">
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="100">Small (100px)</SelectItem>
                    <SelectItem value="200">Medium (200px)</SelectItem>
                    <SelectItem value="300">Large (300px)</SelectItem>
                    <SelectItem value="400">Extra Large (400px)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="qr-level">Error Correction</Label>
                <Select value={qrLevel} onValueChange={(value) => setQrLevel(value as "L" | "M" | "Q" | "H")}>
                  <SelectTrigger id="qr-level">
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="L">Low (7%)</SelectItem>
                    <SelectItem value="M">Medium (15%)</SelectItem>
                    <SelectItem value="Q">Quartile (25%)</SelectItem>
                    <SelectItem value="H">High (30%)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="qr-color">Foreground Color</Label>
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 rounded-md border" style={{ backgroundColor: qrColor }} />
                  <Input
                    id="qr-color"
                    type="color"
                    value={qrColor}
                    onChange={(e) => setQrColor(e.target.value)}
                    className="h-8 w-full"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="qr-bg-color">Background Color</Label>
                <div className="flex items-center space-x-2">
                  <div className="h-8 w-8 rounded-md border" style={{ backgroundColor: qrBgColor }} />
                  <Input
                    id="qr-bg-color"
                    type="color"
                    value={qrBgColor}
                    onChange={(e) => setQrBgColor(e.target.value)}
                    className="h-8 w-full"
                  />
                </div>
              </div>
            </div>

            <div className="rounded-lg border p-4">
              <div className="text-sm text-muted-foreground">
                <p className="mb-2 font-medium">Tips for QR Codes:</p>
                <ul className="list-inside list-disc space-y-1">
                  <li>Higher error correction allows QR codes to be readable even if partially damaged</li>
                  <li>Ensure good contrast between foreground and background colors</li>
                  <li>Test your QR code with different devices before printing</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex flex-col gap-2 sm:flex-row">
          <Button variant="outline" className="w-full sm:w-auto" onClick={handleShareQR}>
            <Share2Icon className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" className="w-full sm:w-auto" onClick={handleCopyLink}>
            <CopyIcon className="mr-2 h-4 w-4" />
            Copy Link
          </Button>
          <Button className="w-full sm:w-auto" onClick={handleDownloadQR}>
            <DownloadIcon className="mr-2 h-4 w-4" />
            Download
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
