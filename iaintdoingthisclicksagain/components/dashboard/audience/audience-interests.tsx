"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

export function AudienceInterests() {
  const interests = [
    { name: "Technology", percentage: 85, count: 106158, trend: "up" },
    { name: "Business", percentage: 72, count: 89923, trend: "up" },
    { name: "Marketing", percentage: 68, count: 84927, trend: "stable" },
    { name: "Design", percentage: 65, count: 81180, trend: "up" },
    { name: "Entrepreneurship", percentage: 58, count: 72437, trend: "up" },
    { name: "Social Media", percentage: 55, count: 68691, trend: "stable" },
    { name: "Innovation", percentage: 52, count: 64944, trend: "down" },
    { name: "Productivity", percentage: 48, count: 59948, trend: "up" },
    { name: "Leadership", percentage: 45, count: 56202, trend: "stable" },
    { name: "Digital Marketing", percentage: 42, count: 52455, trend: "up" },
  ]

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "bg-emerald-100 text-emerald-800"
      case "down":
        return "bg-red-100 text-red-800"
      case "stable":
        return "bg-gray-100 text-gray-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return "↗"
      case "down":
        return "↘"
      case "stable":
        return "→"
      default:
        return "→"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Audience Interests</CardTitle>
        <CardDescription>Top interests and topics your audience engages with</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {interests.map((interest, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{interest.name}</span>
                  <Badge variant="outline" className={getTrendColor(interest.trend)}>
                    {getTrendIcon(interest.trend)} {interest.trend}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">{interest.count.toLocaleString()} followers</div>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">{interest.percentage}% of audience</span>
              </div>
              <Progress value={interest.percentage} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
