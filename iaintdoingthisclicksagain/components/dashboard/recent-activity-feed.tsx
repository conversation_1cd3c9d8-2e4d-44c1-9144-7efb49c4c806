"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Facebook, Instagram, Linkedin, Twitter } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface ActivityItem {
  id: string
  type: "post" | "engagement" | "milestone" | "alert"
  platform: "facebook" | "instagram" | "twitter" | "linkedin"
  message: string
  time: string
  user?: {
    name: string
    avatar?: string
    initials: string
  }
}

const activityData: ActivityItem[] = [
  {
    id: "1",
    type: "post",
    platform: "instagram",
    message: "Your post received 150 likes in the first hour",
    time: "35 minutes ago",
    user: {
      name: "Marketing Team",
      initials: "MT",
    },
  },
  {
    id: "2",
    type: "engagement",
    platform: "facebook",
    message: "Someone shared your post about product updates",
    time: "2 hours ago",
  },
  {
    id: "3",
    type: "milestone",
    platform: "linkedin",
    message: "Your company page reached 5,000 followers!",
    time: "Yesterday",
  },
  {
    id: "4",
    type: "alert",
    platform: "twitter",
    message: "Unusual spike in mentions detected",
    time: "Yesterday",
  },
  {
    id: "5",
    type: "post",
    platform: "facebook",
    message: "Scheduled post about summer sale was published",
    time: "2 days ago",
    user: {
      name: "<PERSON>",
      avatar: "/placeholder.svg?height=32&width=32",
      initials: "SJ",
    },
  },
]

export function RecentActivityFeed() {
  // Function to render platform icon
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "facebook":
        return <Facebook className="h-4 w-4 text-blue-600" />
      case "instagram":
        return <Instagram className="h-4 w-4 text-pink-600" />
      case "twitter":
        return <Twitter className="h-4 w-4 text-blue-400" />
      case "linkedin":
        return <Linkedin className="h-4 w-4 text-blue-700" />
      default:
        return null
    }
  }

  // Function to get badge variant based on activity type
  const getBadgeVariant = (type: string) => {
    switch (type) {
      case "post":
        return "default"
      case "engagement":
        return "secondary"
      case "milestone":
        return "success"
      case "alert":
        return "destructive"
      default:
        return "outline"
    }
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        {activityData.map((activity) => (
          <div key={activity.id} className="flex items-start gap-3 rounded-lg p-2 hover:bg-muted/50">
            <div className="mt-0.5 flex h-8 w-8 items-center justify-center rounded-full bg-muted">
              {getPlatformIcon(activity.platform)}
            </div>
            <div className="flex-1 space-y-1">
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium leading-none">{activity.message}</p>
                <Badge variant={getBadgeVariant(activity.type)} className="ml-auto">
                  {activity.type}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <p className="text-xs text-muted-foreground">{activity.time}</p>
                {activity.user && (
                  <div className="ml-auto flex items-center gap-1">
                    <Avatar className="h-5 w-5">
                      {activity.user.avatar && (
                        <AvatarImage src={activity.user.avatar || "/placeholder.svg"} alt={activity.user.name} />
                      )}
                      <AvatarFallback className="text-[10px]">{activity.user.initials}</AvatarFallback>
                    </Avatar>
                    <span className="text-xs text-muted-foreground">{activity.user.name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
