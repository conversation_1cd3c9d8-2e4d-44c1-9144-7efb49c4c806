"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowUp, ArrowDown } from "lucide-react"

export function EngagementByPlatform() {
  const platforms = [
    {
      name: "Instagram",
      engagementRate: 6.8,
      totalEngagements: 34500,
      growth: 15.2,
      trend: "up",
      color: "bg-gradient-to-r from-purple-500 to-pink-500",
      breakdown: { likes: 78, comments: 12, shares: 10 },
    },
    {
      name: "TikTok",
      engagementRate: 8.2,
      totalEngagements: 28900,
      growth: 32.1,
      trend: "up",
      color: "bg-black",
      breakdown: { likes: 65, comments: 25, shares: 10 },
    },
    {
      name: "LinkedIn",
      engagementRate: 4.1,
      totalEngagements: 15600,
      growth: 8.7,
      trend: "up",
      color: "bg-blue-600",
      breakdown: { likes: 60, comments: 30, shares: 10 },
    },
    {
      name: "Facebook",
      engagementRate: 2.9,
      totalEngagements: 8200,
      growth: -5.3,
      trend: "down",
      color: "bg-blue-500",
      breakdown: { likes: 70, comments: 15, shares: 15 },
    },
    {
      name: "Twitter",
      engagementRate: 2.1,
      totalEngagements: 6800,
      growth: 3.2,
      trend: "up",
      color: "bg-sky-500",
      breakdown: { likes: 55, comments: 20, shares: 25 },
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Engagement by Platform</CardTitle>
        <CardDescription>Compare engagement rates across social media platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {platforms.map((platform) => (
            <div key={platform.name} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`h-3 w-3 rounded-full ${platform.color}`} />
                  <span className="font-medium">{platform.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{platform.engagementRate}%</span>
                  <Badge variant="outline">
                    {platform.trend === "up" ? (
                      <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
                    ) : (
                      <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
                    )}
                    {Math.abs(platform.growth)}%
                  </Badge>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground">Total</div>
                  <div className="font-medium">{platform.totalEngagements.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Likes</div>
                  <div className="font-medium">{platform.breakdown.likes}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Comments</div>
                  <div className="font-medium">{platform.breakdown.comments}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Shares</div>
                  <div className="font-medium">{platform.breakdown.shares}%</div>
                </div>
              </div>
              <Progress value={platform.engagementRate * 10} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
