"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export function EngagementHeatmap() {
  const days = ["Mon", "<PERSON><PERSON>", "<PERSON>d", "<PERSON>hu", "<PERSON>i", "Sat", "Sun"]
  const hours = Array.from({ length: 24 }, (_, i) => i)

  // Mock engagement data (0-100 scale)
  const engagementData = days.map((day) =>
    hours.map((hour) => {
      // Simulate higher engagement during typical active hours
      let base = 20
      if (hour >= 8 && hour <= 10) base += 30 // Morning peak
      if (hour >= 12 && hour <= 14) base += 25 // Lunch peak
      if (hour >= 17 && hour <= 21) base += 40 // Evening peak
      if (day === "Sat" || day === "Sun") base += 15 // Weekend boost

      // Add some randomness
      const random = Math.random() * 20 - 10
      return Math.max(0, Math.min(100, base + random))
    }),
  )

  const getHeatmapColor = (value: number) => {
    if (value >= 80) return "bg-emerald-600"
    if (value >= 60) return "bg-emerald-500"
    if (value >= 40) return "bg-emerald-400"
    if (value >= 20) return "bg-emerald-300"
    return "bg-emerald-100"
  }

  const getHeatmapIntensity = (value: number) => {
    return Math.round(value)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Engagement Heatmap</CardTitle>
        <CardDescription>Optimal posting times based on audience activity</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Best posting times</span>
            <div className="flex items-center gap-2 text-xs">
              <span>Low</span>
              <div className="flex gap-1">
                <div className="h-3 w-3 rounded bg-emerald-100" />
                <div className="h-3 w-3 rounded bg-emerald-300" />
                <div className="h-3 w-3 rounded bg-emerald-400" />
                <div className="h-3 w-3 rounded bg-emerald-500" />
                <div className="h-3 w-3 rounded bg-emerald-600" />
              </div>
              <span>High</span>
            </div>
          </div>

          <div className="overflow-x-auto">
            <div className="grid grid-cols-25 gap-1 text-xs">
              {/* Header row with hours */}
              <div></div>
              {hours.map((hour) => (
                <div key={hour} className="text-center text-muted-foreground">
                  {hour.toString().padStart(2, "0")}
                </div>
              ))}

              {/* Data rows */}
              {days.map((day, dayIndex) => (
                <>
                  <div key={day} className="flex items-center justify-end pr-2 text-muted-foreground">
                    {day}
                  </div>
                  {engagementData[dayIndex].map((value, hourIndex) => (
                    <div
                      key={`${day}-${hourIndex}`}
                      className={`aspect-square rounded ${getHeatmapColor(value)} cursor-pointer transition-all hover:scale-110`}
                      title={`${day} ${hourIndex}:00 - ${getHeatmapIntensity(value)}% engagement`}
                    />
                  ))}
                </>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-2 sm:grid-cols-3">
            <div className="rounded-lg bg-emerald-50 p-3">
              <div className="text-sm font-medium text-emerald-800">Peak Hours</div>
              <div className="text-xs text-emerald-600">6-9 PM weekdays</div>
            </div>
            <div className="rounded-lg bg-blue-50 p-3">
              <div className="text-sm font-medium text-blue-800">Good Hours</div>
              <div className="text-xs text-blue-600">12-2 PM daily</div>
            </div>
            <div className="rounded-lg bg-orange-50 p-3">
              <div className="text-sm font-medium text-orange-800">Avoid</div>
              <div className="text-xs text-orange-600">Late night hours</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
