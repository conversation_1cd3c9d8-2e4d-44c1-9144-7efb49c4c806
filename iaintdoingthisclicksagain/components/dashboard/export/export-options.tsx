"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DateRangeSelector } from "@/components/dashboard/date-range-selector"
import { Download, FileSpreadsheet, FileText, File } from "lucide-react"

const exportTypes = [
  { id: "analytics", label: "Analytics Data", description: "Performance metrics, engagement data" },
  { id: "campaigns", label: "Campaign Data", description: "Campaign performance and ROI metrics" },
  { id: "content", label: "Content Data", description: "Content performance and engagement" },
  { id: "audience", label: "Audience Data", description: "Demographics and audience insights" },
  { id: "affiliate", label: "Affiliate Links", description: "Link performance and click data" },
]

const exportFormats = [
  { value: "csv", label: "CSV", icon: FileSpreadsheet },
  { value: "excel", label: "Excel", icon: FileSpreadsheet },
  { value: "pdf", label: "PDF Report", icon: FileText },
  { value: "json", label: "JSON", icon: File },
]

export function ExportOptions() {
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [format, setFormat] = useState<string>("")

  const handleTypeChange = (typeId: string, checked: boolean) => {
    if (checked) {
      setSelectedTypes([...selectedTypes, typeId])
    } else {
      setSelectedTypes(selectedTypes.filter((id) => id !== typeId))
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Export Options</CardTitle>
        <CardDescription>Select the data types and format for your export</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h4 className="text-sm font-medium mb-3">Data Types</h4>
          <div className="space-y-3">
            {exportTypes.map((type) => (
              <div key={type.id} className="flex items-start space-x-3">
                <Checkbox
                  id={type.id}
                  checked={selectedTypes.includes(type.id)}
                  onCheckedChange={(checked) => handleTypeChange(type.id, checked as boolean)}
                />
                <div className="grid gap-1.5 leading-none">
                  <label
                    htmlFor={type.id}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {type.label}
                  </label>
                  <p className="text-xs text-muted-foreground">{type.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-3">Date Range</h4>
          <DateRangeSelector />
        </div>

        <div>
          <h4 className="text-sm font-medium mb-3">Export Format</h4>
          <Select value={format} onValueChange={setFormat}>
            <SelectTrigger>
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              {exportFormats.map((fmt) => (
                <SelectItem key={fmt.value} value={fmt.value}>
                  <div className="flex items-center">
                    <fmt.icon className="mr-2 h-4 w-4" />
                    {fmt.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button className="w-full" disabled={selectedTypes.length === 0 || !format}>
          <Download className="mr-2 h-4 w-4" />
          Export Data
        </Button>
      </CardContent>
    </Card>
  )
}
