"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Bar, BarChart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar } from "@/components/ui/calendar"

const contentTypeData = [
  { name: "Images", engagement: 1250, reach: 15000, clicks: 320 },
  { name: "Videos", engagement: 2800, reach: 22000, clicks: 650 },
  { name: "Carousels", engagement: 1800, reach: 18000, clicks: 420 },
  { name: "Stories", engagement: 1500, reach: 12000, clicks: 280 },
  { name: "Reels", engagement: 3200, reach: 28000, clicks: 780 },
  { name: "Text", engagement: 800, reach: 8000, clicks: 150 },
]

const abTestData = [
  { name: "Variation A", ctr: 2.8, convRate: 3.2 },
  { name: "Variation B", ctr: 3.5, convRate: 4.1 },
]

const calendarEvents = [
  { date: new Date(2023, 6, 5), title: "Product Launch Post", platform: "Instagram", status: "published" },
  { date: new Date(2023, 6, 8), title: "Customer Testimonial", platform: "Facebook", status: "published" },
  { date: new Date(2023, 6, 12), title: "Industry Report", platform: "LinkedIn", status: "scheduled" },
  { date: new Date(2023, 6, 15), title: "Tutorial Video", platform: "YouTube", status: "scheduled" },
  { date: new Date(2023, 6, 18), title: "Product Feature", platform: "Twitter", status: "draft" },
  { date: new Date(2023, 6, 22), title: "Behind the Scenes", platform: "Instagram", status: "draft" },
  { date: new Date(2023, 6, 25), title: "Case Study", platform: "LinkedIn", status: "scheduled" },
]

export function ContentPerformanceSection() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Content Performance</CardTitle>
        <CardDescription>Analyze engagement metrics by content type and format</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="content-types">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="content-types">Content Types</TabsTrigger>
            <TabsTrigger value="ab-testing">A/B Testing</TabsTrigger>
            <TabsTrigger value="calendar">Publishing Calendar</TabsTrigger>
          </TabsList>
          <TabsContent value="content-types" className="pt-4">
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={contentTypeData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="engagement" name="Engagement" fill="#4f46e5" />
                  <Bar dataKey="clicks" name="Clicks" fill="#a855f7" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-6 grid grid-cols-3 gap-4">
              <div className="rounded-lg border p-3 text-center">
                <div className="text-sm font-medium">Best Performing</div>
                <div className="text-lg font-bold">Reels</div>
                <div className="text-xs text-muted-foreground">3,200 engagements</div>
              </div>
              <div className="rounded-lg border p-3 text-center">
                <div className="text-sm font-medium">Highest CTR</div>
                <div className="text-lg font-bold">Videos</div>
                <div className="text-xs text-muted-foreground">2.95% click-through rate</div>
              </div>
              <div className="rounded-lg border p-3 text-center">
                <div className="text-sm font-medium">Recommended Focus</div>
                <div className="text-lg font-bold">Video Content</div>
                <div className="text-xs text-muted-foreground">Based on engagement trends</div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="ab-testing" className="pt-4">
            <div className="h-[350px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={abTestData}
                  margin={{
                    top: 20,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="ctr" name="Click-Through Rate (%)" fill="#4f46e5" />
                  <Bar dataKey="convRate" name="Conversion Rate (%)" fill="#a855f7" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="mt-6 grid grid-cols-2 gap-6">
              <div className="rounded-lg border p-4">
                <div className="mb-2 text-base font-medium">Variation A</div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Headline:</span>
                    <span className="text-sm font-medium">"Limited Time Offer"</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Image:</span>
                    <span className="text-sm font-medium">Product in use</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">CTA:</span>
                    <span className="text-sm font-medium">"Shop Now"</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">CTR:</span>
                    <span className="text-sm font-medium">2.8%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Conversion:</span>
                    <span className="text-sm font-medium">3.2%</span>
                  </div>
                </div>
              </div>
              <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-900 dark:bg-green-950/30">
                <div className="mb-2 flex items-center gap-2 text-base font-medium">
                  <span>Variation B</span>
                  <Badge
                    variant="outline"
                    className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300"
                  >
                    Winner
                  </Badge>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Headline:</span>
                    <span className="text-sm font-medium">"Save 25% Today Only"</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Image:</span>
                    <span className="text-sm font-medium">Lifestyle shot</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">CTA:</span>
                    <span className="text-sm font-medium">"Get Your Discount"</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">CTR:</span>
                    <span className="text-sm font-medium">3.5%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Conversion:</span>
                    <span className="text-sm font-medium">4.1%</span>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="calendar" className="pt-4">
            <div className="flex flex-col gap-4 lg:flex-row">
              <div className="w-full lg:w-1/2">
                <Calendar mode="single" className="rounded-md border" />
              </div>
              <div className="w-full space-y-4 lg:w-1/2">
                <div className="text-sm font-medium">Upcoming Content</div>
                <div className="space-y-3">
                  {calendarEvents.map((event, index) => (
                    <div key={index} className="flex items-center gap-3 rounded-lg border p-3">
                      <Avatar className="h-9 w-9">
                        <AvatarImage src={`/placeholder.svg?height=36&width=36`} alt={event.platform} />
                        <AvatarFallback>{event.platform.substring(0, 2)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="text-sm font-medium">{event.title}</div>
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <span>{event.platform}</span>
                          <span>•</span>
                          <span>{event.date.toLocaleDateString()}</span>
                        </div>
                      </div>
                      <Badge
                        variant={
                          event.status === "published"
                            ? "default"
                            : event.status === "scheduled"
                              ? "outline"
                              : "secondary"
                        }
                        className="capitalize"
                      >
                        {event.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
