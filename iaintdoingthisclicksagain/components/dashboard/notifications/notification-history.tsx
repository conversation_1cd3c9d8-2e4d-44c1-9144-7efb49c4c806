import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TrendingUp, AlertTriangle, Users, MessageSquare, Settings, Clock, MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const notifications = [
  {
    id: 1,
    type: "performance",
    title: "High Engagement Alert",
    message: "Your latest Instagram post has reached 150% above average engagement rate",
    timestamp: "2 minutes ago",
    read: false,
    priority: "high",
    icon: TrendingUp,
  },
  {
    id: 2,
    type: "milestone",
    title: "Follower Milestone Reached",
    message: "Congratulations! You've reached 10,000 followers on LinkedIn",
    timestamp: "1 hour ago",
    read: false,
    priority: "medium",
    icon: Users,
  },
  {
    id: 3,
    type: "alert",
    title: "Campaign Budget Alert",
    message: "Your Facebook campaign 'Holiday Sale' has spent 80% of its budget",
    timestamp: "3 hours ago",
    read: true,
    priority: "high",
    icon: Alert<PERSON>riangle,
  },
  {
    id: 4,
    type: "mention",
    title: "Brand Mention",
    message: "Your brand was mentioned in a post by @influencer_user",
    timestamp: "5 hours ago",
    read: true,
    priority: "low",
    icon: MessageSquare,
  },
  {
    id: 5,
    type: "system",
    title: "System Maintenance",
    message: "Scheduled maintenance will occur tonight from 2-4 AM EST",
    timestamp: "1 day ago",
    read: true,
    priority: "low",
    icon: Settings,
  },
]

export function NotificationHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Notifications</CardTitle>
        <CardDescription>Your latest alerts and updates</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {notifications.map((notification) => {
            const Icon = notification.icon
            return (
              <div
                key={notification.id}
                className={`flex items-start space-x-4 p-4 rounded-lg border ${
                  !notification.read ? "bg-muted/50" : ""
                }`}
              >
                <div
                  className={`flex-shrink-0 p-2 rounded-full ${
                    notification.priority === "high"
                      ? "bg-red-100 text-red-600"
                      : notification.priority === "medium"
                        ? "bg-yellow-100 text-yellow-600"
                        : "bg-blue-100 text-blue-600"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">{notification.title}</p>
                    {!notification.read && <div className="h-2 w-2 bg-blue-600 rounded-full"></div>}
                    <Badge
                      variant={
                        notification.priority === "high"
                          ? "destructive"
                          : notification.priority === "medium"
                            ? "default"
                            : "secondary"
                      }
                      className="text-xs"
                    >
                      {notification.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                  <div className="flex items-center space-x-1 text-xs text-muted-foreground mt-2">
                    <Clock className="h-3 w-3" />
                    <span>{notification.timestamp}</span>
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>Mark as Read</DropdownMenuItem>
                    <DropdownMenuItem>Archive</DropdownMenuItem>
                    <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
