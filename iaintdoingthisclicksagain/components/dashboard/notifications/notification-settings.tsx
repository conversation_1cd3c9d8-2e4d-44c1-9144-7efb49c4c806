import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const notificationTypes = [
  {
    id: "performance_alerts",
    label: "Performance Alerts",
    description: "Get notified when metrics exceed thresholds",
    enabled: true,
  },
  {
    id: "campaign_updates",
    label: "Campaign Updates",
    description: "Updates on campaign status and performance",
    enabled: true,
  },
  {
    id: "content_mentions",
    label: "Content Mentions",
    description: "When your content is mentioned or shared",
    enabled: false,
  },
  {
    id: "audience_milestones",
    label: "Audience Milestones",
    description: "Follower count and engagement milestones",
    enabled: true,
  },
  {
    id: "system_updates",
    label: "System Updates",
    description: "Platform updates and maintenance notices",
    enabled: true,
  },
]

export function NotificationSettings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Settings</CardTitle>
        <CardDescription>Configure your notification preferences</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h4 className="text-sm font-medium mb-3">Notification Types</h4>
          <div className="space-y-4">
            {notificationTypes.map((type) => (
              <div key={type.id} className="flex items-center justify-between space-x-2">
                <div className="space-y-0.5">
                  <Label htmlFor={type.id} className="text-sm font-medium">
                    {type.label}
                  </Label>
                  <p className="text-xs text-muted-foreground">{type.description}</p>
                </div>
                <Switch id={type.id} defaultChecked={type.enabled} />
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-3">Delivery Method</h4>
          <Select defaultValue="email">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="email">Email Only</SelectItem>
              <SelectItem value="push">Push Only</SelectItem>
              <SelectItem value="both">Email + Push</SelectItem>
              <SelectItem value="none">None</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-3">Frequency</h4>
          <Select defaultValue="immediate">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="immediate">Immediate</SelectItem>
              <SelectItem value="hourly">Hourly Digest</SelectItem>
              <SelectItem value="daily">Daily Digest</SelectItem>
              <SelectItem value="weekly">Weekly Summary</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )
}
