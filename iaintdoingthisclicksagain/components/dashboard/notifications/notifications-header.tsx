import { <PERSON><PERSON> } from "@/components/ui/button"
import { Setting<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react"

export function NotificationsHeader() {
  return (
    <div className="flex items-center justify-between space-y-2">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Notifications</h2>
        <p className="text-muted-foreground">
          Manage your notification preferences and stay updated with important alerts
        </p>
      </div>
      <div className="flex items-center space-x-2">
        <Button variant="outline" size="sm">
          <CheckCheck className="mr-2 h-4 w-4" />
          Mark <PERSON> Read
        </Button>
        <Button size="sm">
          <Settings className="mr-2 h-4 w-4" />
          Settings
        </Button>
      </div>
    </div>
  )
}
