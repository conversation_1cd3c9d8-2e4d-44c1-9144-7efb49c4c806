"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Facebook, Instagram, Linkedin, Twitter, Calendar, Clock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface UpcomingPost {
  id: string
  title: string
  platform: "facebook" | "instagram" | "twitter" | "linkedin"
  scheduledDate: string
  scheduledTime: string
  status: "scheduled" | "draft" | "pending-approval"
  thumbnail?: string
}

const upcomingPosts: UpcomingPost[] = [
  {
    id: "1",
    title: "New Product Launch Announcement",
    platform: "facebook",
    scheduledDate: "2023-06-15",
    scheduledTime: "09:00 AM",
    status: "scheduled",
    thumbnail: "/placeholder.svg?height=60&width=60",
  },
  {
    id: "2",
    title: "Behind the scenes at our office",
    platform: "instagram",
    scheduledDate: "2023-06-16",
    scheduledTime: "12:00 PM",
    status: "scheduled",
    thumbnail: "/placeholder.svg?height=60&width=60",
  },
  {
    id: "3",
    title: "Industry insights from our CEO",
    platform: "linkedin",
    scheduledDate: "2023-06-17",
    scheduledTime: "10:30 AM",
    status: "draft",
  },
  {
    id: "4",
    title: "Customer success story: XYZ Corp",
    platform: "twitter",
    scheduledDate: "2023-06-18",
    scheduledTime: "02:00 PM",
    status: "pending-approval",
  },
]

export function UpcomingContent() {
  // Function to render platform icon
  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "facebook":
        return <Facebook className="h-4 w-4 text-blue-600" />
      case "instagram":
        return <Instagram className="h-4 w-4 text-pink-600" />
      case "twitter":
        return <Twitter className="h-4 w-4 text-blue-400" />
      case "linkedin":
        return <Linkedin className="h-4 w-4 text-blue-700" />
      default:
        return null
    }
  }

  // Function to get badge variant based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "scheduled":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            Scheduled
          </Badge>
        )
      case "draft":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
            Draft
          </Badge>
        )
      case "pending-approval":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
            Pending Approval
          </Badge>
        )
      default:
        return null
    }
  }

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" })
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Upcoming Posts</h3>
        <Button variant="ghost" size="sm" className="h-8 gap-1 text-xs">
          <Calendar className="h-3.5 w-3.5" />
          View Calendar
        </Button>
      </div>

      <div className="space-y-3">
        {upcomingPosts.map((post) => (
          <Card key={post.id} className="overflow-hidden">
            <CardContent className="p-3">
              <div className="flex gap-3">
                {post.thumbnail ? (
                  <div className="h-15 w-15 flex-shrink-0 overflow-hidden rounded-md">
                    <img src={post.thumbnail || "/placeholder.svg"} alt="" className="h-full w-full object-cover" />
                  </div>
                ) : (
                  <div className="flex h-15 w-15 flex-shrink-0 items-center justify-center rounded-md bg-muted">
                    {getPlatformIcon(post.platform)}
                  </div>
                )}

                <div className="flex flex-1 flex-col justify-between">
                  <div className="space-y-1.5">
                    <div className="flex items-center gap-2">
                      {getPlatformIcon(post.platform)}
                      <span className="text-xs font-medium">{post.title}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(post.scheduledDate)}</span>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{post.scheduledTime}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-2 flex items-center justify-between">
                    {getStatusBadge(post.status)}
                    <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
