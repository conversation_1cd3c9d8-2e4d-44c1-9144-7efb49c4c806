"use client"

import type React from "react"

import { useState } from "react"
import { ExternalLink } from "lucide-react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface PlatformField {
  id: string
  label: string
  type: "text" | "password"
  placeholder: string
  required: boolean
}

interface PlatformConfig {
  id: string
  name: string
  icon: React.ElementType
  fields: PlatformField[]
  description: string
  docsUrl: string
}

interface PlatformConnectionFormProps {
  platform: PlatformConfig
  onSubmit: (platformId: string, formData: Record<string, string>) => void
  onCancel: () => void
}

export function PlatformConnectionForm({ platform, onSubmit, onCancel }: PlatformConnectionFormProps) {
  const [formData, setFormData] = useState<Record<string, string>>({})
  const [formTab, setFormTab] = useState<"manual" | "oauth">("manual")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (fieldId: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [fieldId]: value,
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate API call
    setTimeout(() => {
      onSubmit(platform.id, formData)
      setIsSubmitting(false)
    }, 1500)
  }

  const handleOAuthConnect = () => {
    setIsSubmitting(true)

    // Simulate OAuth flow
    setTimeout(() => {
      // In a real app, you would redirect to the OAuth authorization URL
      // and handle the callback with the authorization code
      const mockOAuthData = {
        access_token: "mock_oauth_access_token",
        refresh_token: "mock_oauth_refresh_token",
      }

      onSubmit(platform.id, mockOAuthData)
      setIsSubmitting(false)
    }, 1500)
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <platform.icon className="h-6 w-6" />
          <CardTitle>Connect to {platform.name}</CardTitle>
        </div>
        <CardDescription>{platform.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="manual" value={formTab} onValueChange={(value) => setFormTab(value as "manual" | "oauth")}>
          <TabsList className="mb-4">
            <TabsTrigger value="manual">Manual Setup</TabsTrigger>
            <TabsTrigger value="oauth">OAuth</TabsTrigger>
          </TabsList>

          <TabsContent value="manual">
            <form onSubmit={handleSubmit} className="space-y-4">
              {platform.fields.map((field) => (
                <div key={field.id} className="space-y-2">
                  <Label htmlFor={field.id}>
                    {field.label}
                    {field.required && <span className="text-red-500">*</span>}
                  </Label>
                  <Input
                    id={field.id}
                    type={field.type}
                    placeholder={field.placeholder}
                    value={formData[field.id] || ""}
                    onChange={(e) => handleInputChange(field.id, e.target.value)}
                    required={field.required}
                  />
                </div>
              ))}

              <Alert variant="outline" className="mt-4">
                <AlertDescription className="flex items-center text-sm">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  <span>
                    Need help? Check the{" "}
                    <a
                      href={platform.docsUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium underline"
                    >
                      {platform.name} API documentation
                    </a>
                  </span>
                </AlertDescription>
              </Alert>
            </form>
          </TabsContent>

          <TabsContent value="oauth">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Connect to {platform.name} using OAuth for a simpler setup. This will redirect you to {platform.name} to
                authorize access to your account.
              </p>

              <Button type="button" className="w-full" onClick={handleOAuthConnect} disabled={isSubmitting}>
                {isSubmitting ? "Connecting..." : `Connect with ${platform.name}`}
              </Button>

              <Alert variant="outline">
                <AlertDescription className="text-sm">
                  By connecting, you authorize this application to access your {platform.name} data according to the
                  selected permissions.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        {formTab === "manual" && (
          <Button type="submit" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? "Connecting..." : "Connect"}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
