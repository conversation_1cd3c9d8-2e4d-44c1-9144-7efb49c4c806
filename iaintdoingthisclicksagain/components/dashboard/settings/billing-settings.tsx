"use client"

import { Check, CreditCard } from "lucide-react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { toast } from "@/components/ui/use-toast"

export function BillingSettings() {
  const handleUpgrade = () => {
    toast({
      title: "Upgrade initiated",
      description: "You'll be redirected to complete your upgrade to the Pro plan.",
    })
  }

  const handleUpdatePayment = () => {
    toast({
      title: "Payment method update",
      description: "You'll be redirected to update your payment method.",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Billing & Subscription</CardTitle>
        <CardDescription>Manage your subscription plan and payment methods</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Current Plan</h3>

          <div className="rounded-lg border p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium">Standard Plan</h4>
                  <Badge variant="outline" className="text-xs">
                    Current
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">$49/month, billed monthly</p>
              </div>
              <Button onClick={handleUpgrade}>Upgrade</Button>
            </div>

            <div className="mt-4 space-y-2">
              <p className="text-sm font-medium">Includes:</p>
              <ul className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Up to 5 team members
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  10 social accounts
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  30 days data history
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Basic analytics
                </li>
              </ul>
            </div>
          </div>

          <div className="rounded-lg border border-dashed p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium">Pro Plan</h4>
                  <Badge className="bg-primary text-xs">Recommended</Badge>
                </div>
                <p className="text-sm text-muted-foreground">$99/month, billed monthly</p>
              </div>
              <Button variant="outline" onClick={handleUpgrade}>
                Upgrade
              </Button>
            </div>

            <div className="mt-4 space-y-2">
              <p className="text-sm font-medium">Everything in Standard, plus:</p>
              <ul className="grid grid-cols-1 gap-2 text-sm md:grid-cols-2">
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Unlimited team members
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Unlimited social accounts
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  12 months data history
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Advanced analytics
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Custom reporting
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  Priority support
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Payment Method</h3>

          <div className="rounded-lg border p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-6 w-6" />
                <div>
                  <p className="font-medium">Visa ending in 4242</p>
                  <p className="text-sm text-muted-foreground">Expires 12/2025</p>
                </div>
              </div>
              <Button variant="outline" size="sm" onClick={handleUpdatePayment}>
                Update
              </Button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Billing History</h3>

          <div className="rounded-lg border">
            <div className="grid grid-cols-4 gap-4 border-b p-4 font-medium">
              <div>Date</div>
              <div>Description</div>
              <div>Amount</div>
              <div className="text-right">Invoice</div>
            </div>
            <div className="grid grid-cols-4 gap-4 border-b p-4">
              <div className="text-sm">May 1, 2023</div>
              <div className="text-sm">Standard Plan - Monthly</div>
              <div className="text-sm">$49.00</div>
              <div className="text-right">
                <Button variant="link" className="h-auto p-0 text-sm">
                  Download
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4 border-b p-4">
              <div className="text-sm">Apr 1, 2023</div>
              <div className="text-sm">Standard Plan - Monthly</div>
              <div className="text-sm">$49.00</div>
              <div className="text-right">
                <Button variant="link" className="h-auto p-0 text-sm">
                  Download
                </Button>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4 p-4">
              <div className="text-sm">Mar 1, 2023</div>
              <div className="text-sm">Standard Plan - Monthly</div>
              <div className="text-sm">$49.00</div>
              <div className="text-right">
                <Button variant="link" className="h-auto p-0 text-sm">
                  Download
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <p className="text-sm text-muted-foreground">
          Need help with billing?{" "}
          <Button variant="link" className="h-auto p-0 text-sm">
            Contact support
          </Button>
        </p>
      </CardFooter>
    </Card>
  )
}
