"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { toast } from "@/components/ui/use-toast"

export function NotificationSettings() {
  const handleSave = () => {
    toast({
      title: "Notification settings saved",
      description: "Your notification preferences have been updated successfully.",
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Settings</CardTitle>
        <CardDescription>Manage how and when you receive notifications</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Email Notifications</h3>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-reports">Weekly Reports</Label>
              <p className="text-sm text-muted-foreground">Receive weekly performance reports via email</p>
            </div>
            <Switch id="email-reports" defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-alerts">Performance Alerts</Label>
              <p className="text-sm text-muted-foreground">Get notified when metrics exceed or fall below thresholds</p>
            </div>
            <Switch id="email-alerts" defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="email-news">Product Updates</Label>
              <p className="text-sm text-muted-foreground">Stay informed about new features and improvements</p>
            </div>
            <Switch id="email-news" />
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">In-App Notifications</h3>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-comments">Comments & Mentions</Label>
              <p className="text-sm text-muted-foreground">Notify when someone comments or mentions you</p>
            </div>
            <Switch id="app-comments" defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-tasks">Task Assignments</Label>
              <p className="text-sm text-muted-foreground">Notify when you're assigned a new task</p>
            </div>
            <Switch id="app-tasks" defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="app-alerts">Real-time Alerts</Label>
              <p className="text-sm text-muted-foreground">Show alerts for significant metric changes</p>
            </div>
            <Switch id="app-alerts" defaultChecked />
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={handleSave}>Save Preferences</Button>
      </CardFooter>
    </Card>
  )
}
