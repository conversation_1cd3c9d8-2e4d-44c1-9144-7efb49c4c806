"use client"

import type React from "react"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href?: string
  icon?: React.ReactNode
}

const pathMappings: Record<string, BreadcrumbItem[]> = {
  "/dashboard": [{ label: "Dashboard", icon: <Home className="h-4 w-4" /> }],
  "/dashboard/analytics": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Analytics" },
  ],
  "/dashboard/campaigns": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Campaigns" },
  ],
  "/dashboard/content": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Content" },
  ],
  "/dashboard/calendar": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Calendar" },
  ],
  "/dashboard/affiliate-links": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Affiliate Links" },
  ],
  "/dashboard/affiliate-links/ws-config": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Affiliate Links", href: "/dashboard/affiliate-links" },
    { label: "WebSocket Config" },
  ],
  "/dashboard/reports/performance": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Reports" },
    { label: "Performance" },
  ],
  "/dashboard/reports/audience": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Reports" },
    { label: "Audience" },
  ],
  "/dashboard/reports/engagement": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Reports" },
    { label: "Engagement" },
  ],
  "/dashboard/export": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Export" },
  ],
  "/dashboard/notifications": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Notifications" },
  ],
  "/dashboard/settings": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Settings" },
  ],
  "/dashboard/help": [
    { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
    { label: "Help & Support" },
  ],
}

// Handle dynamic routes for affiliate links
function getDynamicBreadcrumbs(pathname: string): BreadcrumbItem[] | null {
  // Match affiliate link analytics pages like /dashboard/affiliate-links/[id]/analytics
  const affiliateLinkAnalyticsMatch = pathname.match(/^\/dashboard\/affiliate-links\/([^/]+)\/analytics$/)
  if (affiliateLinkAnalyticsMatch) {
    const linkId = affiliateLinkAnalyticsMatch[1]
    return [
      { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
      { label: "Affiliate Links", href: "/dashboard/affiliate-links" },
      { label: `Link ${linkId.slice(0, 8)}...`, href: `/dashboard/affiliate-links/${linkId}` },
      { label: "Analytics" },
    ]
  }

  // Match individual affiliate link pages like /dashboard/affiliate-links/[id]
  const affiliateLinkMatch = pathname.match(/^\/dashboard\/affiliate-links\/([^/]+)$/)
  if (affiliateLinkMatch) {
    const linkId = affiliateLinkMatch[1]
    return [
      { label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> },
      { label: "Affiliate Links", href: "/dashboard/affiliate-links" },
      { label: `Link ${linkId.slice(0, 8)}...` },
    ]
  }

  return null
}

export function BreadcrumbNav() {
  const pathname = usePathname()

  // Try to get breadcrumbs from static mappings first
  let breadcrumbs = pathMappings[pathname]

  // If not found, try dynamic routes
  if (!breadcrumbs) {
    breadcrumbs = getDynamicBreadcrumbs(pathname)
  }

  // Fallback for unknown routes
  if (!breadcrumbs) {
    const segments = pathname.split("/").filter(Boolean)
    breadcrumbs = [{ label: "Dashboard", href: "/dashboard", icon: <Home className="h-4 w-4" /> }]

    // Add segments as breadcrumbs
    for (let i = 1; i < segments.length; i++) {
      const segment = segments[i]
      const href = i === segments.length - 1 ? undefined : `/${segments.slice(0, i + 1).join("/")}`
      breadcrumbs.push({
        label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, " "),
        href,
      })
    }
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground mb-4">
      {breadcrumbs.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && <ChevronRight className="h-4 w-4 mx-1" />}
          {item.href ? (
            <Link
              href={item.href}
              className={cn(
                "flex items-center space-x-1 hover:text-foreground transition-colors",
                index === 0 && "text-foreground font-medium",
              )}
            >
              {item.icon}
              <span>{item.label}</span>
            </Link>
          ) : (
            <div className="flex items-center space-x-1 text-foreground font-medium">
              {item.icon}
              <span>{item.label}</span>
            </div>
          )}
        </div>
      ))}
    </nav>
  )
}
