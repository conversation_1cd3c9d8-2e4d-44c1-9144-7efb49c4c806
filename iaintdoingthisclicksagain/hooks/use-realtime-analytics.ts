"use client"
import { useState, useEffect, useCallback, useRef } from "react"
import { toast } from "sonner"
import { useWebSocket, createMockWebSocketServer } from "@/lib/websocket-client"

// Types for real-time data
export interface RealTimeClick {
  id: string
  linkId: string
  timestamp: Date
  country: string
  device: "mobile" | "desktop" | "tablet"
  referrer: string
  converted: boolean
  revenue?: number
}

export interface RealTimeMetrics {
  totalClicks: number
  uniqueClicks: number
  conversions: number
  revenue: number
  clicksToday: number
  conversionRate: number
  revenueToday: number
}

export interface LiveAnalyticsData {
  metrics: RealTimeMetrics
  recentClicks: RealTimeClick[]
  hourlyData: { hour: number; clicks: number; conversions: number }[]
  isLive: boolean
  lastUpdate: Date
}

const countries = ["United States", "United Kingdom", "Canada", "Australia", "Germany", "France", "Japan", "Brazil"]
const devices = ["mobile", "desktop", "tablet"] as const
const referrers = ["Instagram", "Facebook", "Twitter", "Direct", "Email", "Google", "YouTube"]

export function useRealTimeAnalytics(linkId: string, enabled = true) {
  // Create a ref to store previous metrics for comparison
  const prevMetricsRef = useRef<RealTimeMetrics | null>(null)

  // Initialize with default data
  const [data, setData] = useState<LiveAnalyticsData>({
    metrics: {
      totalClicks: 12450,
      uniqueClicks: 8920,
      conversions: 234,
      revenue: 2340.5,
      clicksToday: 45,
      conversionRate: 2.62,
      revenueToday: 125.5,
    },
    recentClicks: [],
    hourlyData: Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      clicks: Math.floor(Math.random() * 200) + 50,
      conversions: Math.floor(Math.random() * 10) + 1,
    })),
    isLive: false,
    lastUpdate: new Date(),
  })

  // Options for notifications
  const [notificationsEnabled, setNotificationsEnabled] = useState(true)

  // Create a mock WebSocket server for development/demo purposes
  const mockServer = createMockWebSocketServer({
    interval: 3000,
    eventTypes: ["click", "conversion", "pageview"],
  })

  // Setup the environment-specific WebSocket URL
  // In production, this would come from an environment variable
  const wsUrl = process.env.NEXT_PUBLIC_ANALYTICS_WS_URL || mockServer.url

  // Handle WebSocket connection with our custom hook
  const { status, lastMessage, isConnected, connect, disconnect, sendMessage } = useWebSocket({
    url: wsUrl,
    reconnectAttempts: 5,
    reconnectInterval: 5000,
    autoReconnect: enabled,

    // Setup callback handlers
    onOpen: () => {
      // When WebSocket connects, send a subscription message for this specific link
      sendMessage({
        type: "subscribe",
        channel: `link_analytics_${linkId}`,
        data: { linkId },
      })

      setData((prev) => ({
        ...prev,
        isLive: true,
      }))
    },

    onClose: () => {
      setData((prev) => ({
        ...prev,
        isLive: false,
      }))
    },
  })

  // Process incoming WebSocket messages
  useEffect(() => {
    if (!lastMessage) return

    // Store previous metrics for comparison
    prevMetricsRef.current = data.metrics

    try {
      // Parse and process the incoming data based on event type
      if (lastMessage.type === "click" || lastMessage.type === "conversion") {
        const clickData: RealTimeClick = {
          ...lastMessage.data,
          timestamp: new Date(lastMessage.data.timestamp),
        }

        // Only process if this is for our link ID
        if (clickData.linkId === linkId) {
          const isConversion = lastMessage.type === "conversion" || clickData.converted

          setData((prevData) => {
            // Update metrics based on event type
            const updatedMetrics = {
              ...prevData.metrics,
              totalClicks: prevData.metrics.totalClicks + 1,
              uniqueClicks: Math.random() < 0.7 ? prevData.metrics.uniqueClicks + 1 : prevData.metrics.uniqueClicks,
              clicksToday: prevData.metrics.clicksToday + 1,
              conversions: isConversion ? prevData.metrics.conversions + 1 : prevData.metrics.conversions,
              revenue:
                isConversion && clickData.revenue
                  ? prevData.metrics.revenue + clickData.revenue
                  : prevData.metrics.revenue,
              revenueToday:
                isConversion && clickData.revenue
                  ? prevData.metrics.revenueToday + clickData.revenue
                  : prevData.metrics.revenueToday,
            }

            // Update conversion rate
            updatedMetrics.conversionRate = (updatedMetrics.conversions / updatedMetrics.totalClicks) * 100

            // Update hourly data
            const currentHour = new Date().getHours()
            const updatedHourlyData = prevData.hourlyData.map((hour) =>
              hour.hour === currentHour
                ? {
                    ...hour,
                    clicks: hour.clicks + 1,
                    conversions: isConversion ? hour.conversions + 1 : hour.conversions,
                  }
                : hour,
            )

            // Keep only last 50 clicks
            const updatedRecentClicks = [clickData, ...prevData.recentClicks].slice(0, 50)

            return {
              metrics: updatedMetrics,
              recentClicks: updatedRecentClicks,
              hourlyData: updatedHourlyData,
              isLive: true,
              lastUpdate: new Date(),
            }
          })

          // Show notification for conversions
          if (notificationsEnabled && isConversion) {
            toast.success(`🎉 New conversion from ${clickData.country}!`, {
              description: `Revenue: $${clickData.revenue?.toFixed(2)} via ${clickData.referrer}`,
              duration: 3000,
            })
          }
        }
      } else if (lastMessage.type === "metrics_update") {
        // Handle bulk metrics updates if the server sends those
        setData((prevData) => ({
          ...prevData,
          metrics: {
            ...prevData.metrics,
            ...lastMessage.data,
          },
          lastUpdate: new Date(),
        }))
      }
    } catch (error) {
      console.error("Error processing WebSocket message:", error)
    }
  }, [lastMessage, linkId, notificationsEnabled])

  // Since we're using a mock WebSocket, simulate events for demo purposes
  useEffect(() => {
    if (!enabled || !isConnected) return

    // This simulates incoming WebSocket messages for the demo
    // In a real implementation, messages would come from the server
    const interval = setInterval(
      () => {
        // Generate mock event
        const mockEvent = mockServer.generateMockMessage()

        // Process as if it came from WebSocket
        const event = new MessageEvent("message", {
          data: JSON.stringify(mockEvent),
        })

        // Simulate onmessage handler
        if (event.data) {
          try {
            const parsedData = JSON.parse(event.data)
            const isConversion = parsedData.type === "conversion" || parsedData.data.converted

            if (parsedData.data.linkId === linkId) {
              // Handle click event
              if (parsedData.type === "click" || parsedData.type === "conversion") {
                const clickData: RealTimeClick = {
                  ...parsedData.data,
                  timestamp: new Date(parsedData.timestamp),
                }

                setData((prevData) => {
                  const updatedMetrics = {
                    ...prevData.metrics,
                    totalClicks: prevData.metrics.totalClicks + 1,
                    uniqueClicks:
                      Math.random() < 0.7 ? prevData.metrics.uniqueClicks + 1 : prevData.metrics.uniqueClicks,
                    clicksToday: prevData.metrics.clicksToday + 1,
                    conversions: isConversion ? prevData.metrics.conversions + 1 : prevData.metrics.conversions,
                    revenue:
                      isConversion && clickData.revenue
                        ? prevData.metrics.revenue + clickData.revenue
                        : prevData.metrics.revenue,
                    revenueToday:
                      isConversion && clickData.revenue
                        ? prevData.metrics.revenueToday + clickData.revenue
                        : prevData.metrics.revenueToday,
                  }

                  updatedMetrics.conversionRate = (updatedMetrics.conversions / updatedMetrics.totalClicks) * 100

                  const currentHour = new Date().getHours()
                  const updatedHourlyData = prevData.hourlyData.map((hour) =>
                    hour.hour === currentHour
                      ? {
                          ...hour,
                          clicks: hour.clicks + 1,
                          conversions: isConversion ? hour.conversions + 1 : hour.conversions,
                        }
                      : hour,
                  )

                  const updatedRecentClicks = [clickData, ...prevData.recentClicks].slice(0, 50)

                  return {
                    metrics: updatedMetrics,
                    recentClicks: updatedRecentClicks,
                    hourlyData: updatedHourlyData,
                    isLive: true,
                    lastUpdate: new Date(),
                  }
                })

                if (notificationsEnabled && isConversion) {
                  toast.success(`🎉 New conversion from ${clickData.country}!`, {
                    description: `Revenue: $${clickData.revenue?.toFixed(2)} via ${clickData.referrer}`,
                    duration: 3000,
                  })
                }
              }
            }
          } catch (err) {
            console.error("Error parsing mock WebSocket message:", err)
          }
        }
      },
      2000 + Math.random() * 4000,
    ) // Random interval between 2-6 seconds

    return () => clearInterval(interval)
  }, [enabled, isConnected, linkId, mockServer, notificationsEnabled])

  // Toggle notifications
  const toggleNotifications = useCallback(() => {
    setNotificationsEnabled((prev) => {
      const newValue = !prev
      toast.info(newValue ? "Notifications enabled" : "Notifications disabled")
      return newValue
    })
  }, [])

  // Start real-time tracking
  const startRealTime = useCallback(() => {
    if (status !== "connected") {
      connect()
    }
    toast.success("Live tracking enabled", {
      description: "You'll now receive real-time click updates",
    })
  }, [connect, status])

  // Stop real-time tracking
  const stopRealTime = useCallback(() => {
    disconnect()
    setData((prev) => ({ ...prev, isLive: false }))
    toast.info("Live tracking paused")
  }, [disconnect])

  // Manual trigger for demo/testing
  const simulateClick = useCallback(() => {
    const mockEvent = mockServer.generateMockMessage()

    // Process as if it came from WebSocket
    const event = new MessageEvent("message", {
      data: JSON.stringify(mockEvent),
    })

    // Simulate message handler
    if (event.data) {
      try {
        const parsedData = JSON.parse(event.data)
        parsedData.data.linkId = linkId // Force link ID to match

        const clickData: RealTimeClick = {
          ...parsedData.data,
          timestamp: new Date(),
        }

        const isConversion = Math.random() < 0.1 // 10% chance of conversion for manual clicks
        clickData.converted = isConversion
        if (isConversion) {
          clickData.revenue = Math.round(Math.random() * 50 + 10)
        }

        setData((prevData) => {
          const updatedMetrics = {
            ...prevData.metrics,
            totalClicks: prevData.metrics.totalClicks + 1,
            uniqueClicks: prevData.metrics.uniqueClicks + 1,
            clicksToday: prevData.metrics.clicksToday + 1,
            conversions: isConversion ? prevData.metrics.conversions + 1 : prevData.metrics.conversions,
            revenue:
              isConversion && clickData.revenue
                ? prevData.metrics.revenue + clickData.revenue
                : prevData.metrics.revenue,
            revenueToday:
              isConversion && clickData.revenue
                ? prevData.metrics.revenueToday + clickData.revenue
                : prevData.metrics.revenueToday,
          }

          updatedMetrics.conversionRate = (updatedMetrics.conversions / updatedMetrics.totalClicks) * 100

          const currentHour = new Date().getHours()
          const updatedHourlyData = prevData.hourlyData.map((hour) =>
            hour.hour === currentHour
              ? {
                  ...hour,
                  clicks: hour.clicks + 1,
                  conversions: isConversion ? hour.conversions + 1 : hour.conversions,
                }
              : hour,
          )

          const updatedRecentClicks = [clickData, ...prevData.recentClicks].slice(0, 50)

          return {
            metrics: updatedMetrics,
            recentClicks: updatedRecentClicks,
            hourlyData: updatedHourlyData,
            isLive: true,
            lastUpdate: new Date(),
          }
        })

        if (notificationsEnabled && isConversion) {
          toast.success(`🎉 New conversion from ${clickData.country}!`, {
            description: `Revenue: $${clickData.revenue?.toFixed(2)} via ${clickData.referrer}`,
            duration: 3000,
          })
        }
      } catch (err) {
        console.error("Error parsing simulated WebSocket message:", err)
      }
    }
  }, [linkId, mockServer, notificationsEnabled])

  // Connect/disconnect based on enabled prop
  useEffect(() => {
    if (enabled && status === "disconnected") {
      connect()
    } else if (!enabled && status === "connected") {
      disconnect()
    }

    // Cleanup on unmount
    return () => {
      if (status === "connected") {
        disconnect()
      }
    }
  }, [connect, disconnect, enabled, status])

  return {
    data,
    status,
    isConnected: status === "connected",
    notificationsEnabled,
    previousMetrics: prevMetricsRef.current,
    startRealTime,
    stopRealTime,
    toggleNotifications,
    simulateClick,
  }
}
