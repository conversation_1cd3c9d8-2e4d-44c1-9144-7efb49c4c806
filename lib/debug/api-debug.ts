import { logger } from './logger';

// Store for API debug info
const apiDebugStore: Record<string, any> = {};
const apiPerformanceMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  totalTime: 0,
  averageTime: 0,
  slowestRequest: { url: '', time: 0 },
  fastestRequest: { url: '', time: Infinity }
};

// Initialize API debugging
export default function initApiDebug() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  // Store original fetch
  const originalFetch = window.fetch;

  // Override fetch with debug version
  window.fetch = async function debugFetch(input, init) {
    const url = typeof input === 'string' ? input : input.url;
    const method = init?.method || (typeof input === 'string' ? 'GET' : input.method) || 'GET';
    const requestId = `${method}-${url}-${Date.now()}`;
    
    // Log request start
    logger.debug(`API Request: ${method} ${url}`, init?.body, 'API');
    
    const startTime = performance.now();
    
    try {
      // Make the actual request
      const response = await originalFetch(input, init);
      
      // Calculate timing
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Clone the response so we can read the body
      const responseClone = response.clone();
      let responseBody;
      
      try {
        // Try to parse as JSON
        responseBody = await responseClone.json();
      } catch (e) {
        // If not JSON, get text
        try {
          responseBody = await responseClone.text();
        } catch (e) {
          responseBody = 'Unable to read response body';
        }
      }
      
      // Store request info
      apiDebugStore[requestId] = {
        url,
        method,
        requestBody: init?.body,
        responseStatus: response.status,
        responseBody,
        duration,
        timestamp: Date.now(),
        headers: Object.fromEntries(response.headers.entries())
      };
      
      // Update metrics
      updateApiMetrics(url, duration, true);
      
      // Log response
      logger.debug(
        `API Response: ${method} ${url} (${response.status})`,
        { duration: `${duration.toFixed(2)}ms`, body: responseBody },
        'API'
      );
      
      return response;
    } catch (error) {
      // Calculate timing for failed requests
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Store error info
      apiDebugStore[requestId] = {
        url,
        method,
        requestBody: init?.body,
        error: error.message,
        duration,
        timestamp: Date.now(),
        failed: true
      };
      
      // Update metrics
      updateApiMetrics(url, duration, false);
      
      // Log error
      logger.error(
        `API Error: ${method} ${url}`,
        { duration: `${duration.toFixed(2)}ms`, error: error.message },
        'API'
      );
      
      throw error;
    }
  };
  
  logger.info('API debugging initialized', null, 'API');
}

function updateApiMetrics(url: string, duration: number, success: boolean) {
  apiPerformanceMetrics.totalRequests++;
  apiPerformanceMetrics.totalTime += duration;
  
  if (success) {
    apiPerformanceMetrics.successfulRequests++;
  } else {
    apiPerformanceMetrics.failedRequests++;
  }
  
  apiPerformanceMetrics.averageTime = 
    apiPerformanceMetrics.totalTime / apiPerformanceMetrics.totalRequests;
  
  if (duration > apiPerformanceMetrics.slowestRequest.time) {
    apiPerformanceMetrics.slowestRequest = { url, time: duration };
  }
  
  if (duration < apiPerformanceMetrics.fastestRequest.time) {
    apiPerformanceMetrics.fastestRequest = { url, time: duration };
  }
}

export function getApiDebugInfo(): Record<string, any> {
  return { ...apiDebugStore };
}

export function clearApiDebugInfo(): void {
  Object.keys(apiDebugStore).forEach(key => {
    delete apiDebugStore[key];
  });
}

export function getApiPerformanceMetrics() {
  return { ...apiPerformanceMetrics };
}