import { useDebugValue as useReactDebugValue, useRef, useEffect } from 'react';
import { logger } from './logger';

export function useDebugHook<T>(value: T, name: string, componentName?: string): void {
  // Only run in development
  if (process.env.NODE_ENV !== 'development') return;

  // Use React's built-in debug value
  useReactDebugValue(value);

  // Track previous value for comparison
  const prevValue = useRef<T>(value);

  useEffect(() => {
    // Log when value changes
    if (JSON.stringify(prevValue.current) !== JSON.stringify(value)) {
      logger.debug(
        `${name} changed in ${componentName || 'component'}`,
        {
          from: prevValue.current,
          to: value
        },
        'useDebugHook'
      );
      prevValue.current = value;
    }
  }, [value, name, componentName]);
}

export function useRenderCount(componentName: string): number {
  // Only track in development
  if (process.env.NODE_ENV !== 'development') return 0;

  const renderCount = useRef(0);

  useEffect(() => {
    renderCount.current += 1;
    logger.debug(
      `${componentName} rendered`,
      { count: renderCount.current },
      'useRenderCount'
    );
  });

  return renderCount.current;
}